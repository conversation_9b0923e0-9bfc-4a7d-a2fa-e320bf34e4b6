import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.styles import numbers
from openpyxl.formatting.rule import DataBarRule
from multiprocessing import Pool
from decimal import Decimal, getcontext


def dict_sj():
    workbook = openpyxl.load_workbook(filename='规则名称.xlsx')
    # 获取默认工作表
    sheet = workbook['Sheet1']
    # 将数据转换为字典
    data = {}
    for row in sheet.iter_rows(min_row=2, values_only=True):
        key = row[0]
        value = row[1:]
        data[key] = value

    return data
def format_excel(ws):
    # 设置字体和颜色,文本对齐方式
    header_font = Font(name='Microsoft YaHei UI', size=10, bold=True, color='FFFFFF')
    content_font = Font(name='Microsoft YaHei UI', size=10)
    header_fill = PatternFill(start_color='1f4e78', end_color='1f4e78', fill_type='solid')
    content_fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
    highlight_fill = PatternFill(start_color='f8cbad', end_color='f8cbad', fill_type='solid')
    
    # 格式化表头
    for row in ws.iter_rows(min_row=1, max_row=1):
        for cell in row:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    # 添加自动筛选
    ws.auto_filter.ref = ws.dimensions
    # 格式化内容
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
            cell.font = content_font
            cell.fill = content_fill
    # 设置行高和列宽
    ws.row_dimensions[1].height = 35  # 表头行
    for r in range(2, ws.max_row):    # 内容行
        ws.row_dimensions[r].height = 25
    # 优化列宽
    dict_width = {'A': 8, 'B': 15, 'C': 15, 'D': 15, 'E': 50, 'F': 50, 'G': 12, 'H': 12}
    # 格式化各列
    for col in ws.columns:
        column = col[0].column_letter
        for i, cell in enumerate(col):
            if i > 0 and (column == 'A'):  # 序号列
                cell.font = Font(name='Microsoft YaHei UI', size=10, color='0000ff', underline='single')
            elif i > 0 and (column == 'D' or column == 'E' or column == 'F' ):  # 问题和规则依据列
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=False)
            elif i > 0 and (column in ['H', 'G']):  # 金额列
                #cell.number_format = numbers.FORMAT_NUMBER_COMMA_SEPARATED1
                cell.fill = PatternFill(start_color='fce4d6', end_color='fce4d6', fill_type='solid')
        ws.column_dimensions[column].width = dict_width.get(column, 12)  # 默认宽度12
    # 设置边框
    thin_border = Border(left=Side(style='thin', color='7F7F7F'),
                        right=Side(style='thin', color='7F7F7F'),
                        top=Side(style='thin', color='7F7F7F'),
                        bottom=Side(style='thin', color='7F7F7F'))
    # 应用边框
    for row in ws.rows:
        for cell in row:
            cell.border = thin_border
    
    # 处理最后一行
    ws.merge_cells(start_row=ws.max_row, start_column=2, end_row=ws.max_row, end_column=6)
    for cell in ws[ws.max_row]:
        cell.font = Font(bold=True, name='Microsoft YaHei UI', size=10)
        cell.fill = highlight_fill
    ws.cell(row=ws.max_row, column=4).alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[ws.max_row].height = 40
    
    # 冻结首行
    ws.freeze_panes = 'A2'
    

    return ws

def merge_xlsx_files(source_folder, target_file,data):
    # 检查源文件夹是否存在
    if not os.path.exists(source_folder):
        print("源文件夹不存在！")
        return
    # 创建一个新工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = '汇总'
    ws.append(['序号', '二级指标', '问题项目', '违规项目编码', '问题情形描述','认定依据','人次','金额'])
    # 遍历源文件夹中的所有文件
    sheet_num = 1
    for filename in os.listdir(source_folder):
        if filename.lower().endswith(".xlsx"):
            # 加载工作簿
            #print(os.path.join(source_folder, filename))
            workbook = openpyxl.load_workbook(os.path.join(source_folder, filename), read_only=True)
            # 遍历工作簿中的所有工作表
            for sheetname in workbook.sheetnames:
                # 复制工作表到新工作簿中的单独工作表中
                sheet = workbook[sheetname]
                new_ws = wb.create_sheet(f"{sheet_num}")
                new_ws.title = f"{sheet_num}"
                # 遍历单元格，将值复制到新的单元格中
                for row in sheet.iter_rows():
                    new_ws.append([cell.value for cell in row])
                last_col = new_ws.max_column
                second_last_col = last_col - 1
                total_violations = 0
                total_amount = 0
                # for col in new_ws.iter_cols(min_col=second_last_col, max_col=last_col):
                #     for cell in col:
                #         if cell.row == 1:
                #             continue
                #         if cell.column == second_last_col:
                #             if cell.value is not None:
                #              try:
                #                 total_violations += float(cell.value)
                #                 cell.value = float(cell.value)
                #                 #设置为数字
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                #         elif cell.column == last_col:
                #             if cell.value is not None:
                #              try:
                #                  # 设置为数字
                #                  total_amount += float(cell.value)
                #                  cell.value = float(cell.value)
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                # 查找money_rules和bill_id列
                money_col = None
                bill_id_col = None
                for col in new_ws.iter_cols(min_row=1, max_row=1):  # 只在表头行查找
                    for cell in col:
                        if cell.value == "违规金额":
                            money_col = cell.column
                        elif cell.value == "单据号":
                            bill_id_col = cell.column
                
                getcontext().prec = 10  # 设置足够高的精度
                
                total_amount = Decimal('0')
                distinct_count = 0
                bill_ids = set()
                if money_col is not None and bill_id_col is not None:
                    for row in new_ws.iter_rows(min_row=2): 
                        # 处理金额列
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')
                        
                        # 处理ID列
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None and str(bill_cell.value).strip():
                            bill_ids.add(str(bill_cell.value).strip())
                    
                    distinct_count = len(bill_ids)
                elif money_col is not None:  # 只有金额列的情况
                    for row in new_ws.iter_rows(min_row=2):
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')
                elif bill_id_col is not None:  # 只有ID列的情况
                    bill_ids = set()
                    for row in new_ws.iter_rows(min_row=2):
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None:
                            bill_ids.add(str(bill_cell.value))
                    distinct_count = len(bill_ids)
                
                #(['序号', '住院/门诊', '规则分类', '规则名称', '规则依据', '金额合计', '不重复bill_id数'])
                gzmc = filename[:-5]
                ws.append([f'=HYPERLINK("#{sheet_num}!A1", "{sheet_num}")', 
                          data.get(gzmc, ['','','','','',''])[0],
                          data.get(gzmc, ['','','','','',''])[1], 
                          data.get(gzmc, ['','','','','',''])[2], 
                          data.get(gzmc, ['','','','','',''])[3],
                          data.get(gzmc, ['','','','','',''])[4], 
                          distinct_count,
                          total_amount]) 
                print(f"已处理文件：{os.path.join(source_folder, filename)}")
                sheet_num += 1
    # 保存新工作簿
    # total_violations = sum([float(cell.value) for i, cell in enumerate(ws['F'], 1) if i > 1])
    # total_amount = sum([float(cell.value) for i, cell in enumerate(ws['G'], 1) if i > 1])
    ws.append(['合计', f'共计-{ws.max_row - 1}-条违规项目', '', f'', '','',
              f'=SUM(G2:G{ws.max_row})',  # 人次总和
              f'=SUM(H2:H{ws.max_row})'])  # 金额总和
    ws = format_excel(ws)
    wb.save(target_file)
def traverse_folders(folder_path, save_path, data):
    process_count = int(input(f"几个进程:"))
    
    # 收集所有任务
    tasks = []
    for root, dirs, files in os.walk(folder_path):
        for name in dirs:
            source_path = os.path.join(root, name)
            result_path = os.path.join(save_path, f'{name}_汇总.xlsx')
            tasks.append((source_path, result_path, data))
    
    # 使用进程池并行处理
    with Pool(processes=process_count) as pool:
        pool.starmap(merge_xlsx_files, tasks)

if __name__ == '__main__':
    source_path = input("请输入要遍历的文件夹地址：")
    save_path = input("请输入要保存的文件夹地址：")
    data = dict_sj() # 获取数据字典
    traverse_folders(source_path, save_path,data) # 调用多进程遍历子目录并合并Excel文件的函数
    input("输入任意键退出：")

