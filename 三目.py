import duckdb
con = duckdb.connect(database='d:/file.db')
con.install_extension('excel')#加载execl控制
con.load_extension('excel')

#results = con.sql(f"""select DISTINCT trim(定点机构名称) from sm WHERE  regexp_matches(定点机构名称,'[^\\x00-\\xff]') """).fetchall()
results = con.sql(fr""" SELECT * FROM 'C:\Users\<USER>\Desktop\1.CSV' """).fetchall()

#print(results)


for row in results:
    sql = f"""COPY (SELECT  定点机构名称, 年份, 医保目录编码, 医保目录名称, 医疗类别, 收费项目类别, 数量, 金额
FROM file.main.sm where 定点机构名称 like '{row[0]}'
	 ) TO 'C:/Users/<USER>/Desktop/新建文件夹/三目/药店/{row[0]}.xlsx' WITH (FORMAT xlsx, HEADER true)"""
    #print(sql)
    print({row[0]})
    con.sql(sql)
