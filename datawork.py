from loguru import logger
from odps import ODPS
from odps.tunnel import TableTunnel
from odps import options
import warnings
import pandas as pd
import multiprocessing
from numpy import array_split
from tqdm import tqdm
import dwj
warnings.filterwarnings("ignore")
options.verify_ssl = False
options.allow_antique_date = True 
logger.add('sql执行', rotation="500 MB")
def limit_sql_query(sql, j=0):
    odber = """ORDER BY BILL_ID,BILL_DETAIL_ID"""
    #odber = """ORDER BY 结算id"""
    #odber = """ORDER BY 金额"""
    odber = ""
    if 'with' in sql.lower():
        index = sql.lower().rfind('select')
        #index = sql.lower().rfind('select',0,index)#找到最后2个select
        new_sql = sql[:index] + f'SELECT * FROM (' + sql[index:] + f""" )   {odber} limit {j},10000"""
    else:
        new_sql = f"""SELECT * FROM ({sql})  {odber} limit {j},10000"""
    return new_sql

def download_odps_table_to_csv(odps_access_id, odps_secret_access_key, project_name, endpoint, output_file, df):
    o = ODPS(odps_access_id, odps_secret_access_key, project_name, endpoint=endpoint)
    dict = {}
    for i, row in df.iterrows():
        logger.info(f"第{i + 1}个正在执行:{row['rule_name']} ")
        if row['sql'].isspace():
            logger.info(f"{row['rule_name']} sql 为空 ")
            continue
        j = 0
        while True:
            sql = limit_sql_query(row["sql"], j)
            #print(sql)
            try:
                with o.execute_sql(sql).open_reader(tunnel=True) as reader:
                    count = reader.count
                    if j == 0:  # 只在第一次查询时记录结果条数
                        with open('sql_result_counts.csv', 'a') as f:
                            f.write(f"""{row['rule_name']},"{row['sql']}",{count}\n""")
                    if count == 0:
                        logger.info(f"{row['rule_name']} sql 查询结果为空 ")
                        break
                    pd_df = reader.to_pandas()
                    if count < 10000 and j == 0:
                        file_path = f"{output_file}/{row['rule_name']}.csv"
                        dict[f"{row['rule_name']}"] = [file_path]
                        pd_df.to_csv(file_path, header=True, index=False)
                    else:
                        file_path = f"{output_file}/{row['rule_name']}_page_{j}.csv"
                        if dict.get(f"{row['rule_name']}") is None:
                            dict[f"{row['rule_name']}"] = [file_path]
                        else:
                            #print(dict)
                            dict[f"{row['rule_name']}"].append(file_path)
                        pd_df.to_csv(f"{output_file}/{row['rule_name']}_page_{j}.csv", header=True, index=False)
                    if count < 10000:
                        break
                    j += 10000
            except Exception as e:
                logger.info(f"{row['rule_name']}: 错误")
                #logger.info(f"{sql}")
                print(e)
                break
    
    dwj.csv_xlsx(dict, f"d:/xzz/aa", split_by_hospital=False)
if __name__ == '__main__':
    df = pd.read_csv(r'C:\Users\<USER>\Desktop\61.csv', dtype=str, encoding='gbk')
    #df = pd.read_excel(r'C:\Users\<USER>\Desktop\fly_rule_jm.xlsx', dtype=str)
    df = df.iloc[-3:]
    df = df[~df['sql'].isnull()]
    #df = df[(df['rule_name'] == '埋针治疗') ]
    #manager = multiprocessing.Manager()#创建管理器
    #dict = manager.dict()#   创建字典
    num_processes = 2 #进程数
    split_dfs = array_split(df, num_processes)
    with multiprocessing.Pool(processes=num_processes) as pool:
        pool.starmap(download_odps_table_to_csv, [
            ('UPRJ9Qlhi3bnm38j', '4uUtT41PzFMAPLWaI1cQIsBK0NRqhQ', 'ls_tjjczq_prd', 'https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api', 'd:/xzz/423', sub_df)
            for sub_df in split_dfs
        ])


