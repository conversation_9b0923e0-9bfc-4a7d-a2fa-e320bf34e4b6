
from playwright.sync_api import sync_playwright
# 使用Playwright连接到浏览器
playwright = sync_playwright().start()
browser = playwright.chromium.launch(headless=False, channel='msedge',args=['--start-maximized'])# 加载cookies
default_context = browser.new_context(ignore_https_errors=True, storage_state="state.json",no_viewport=True)
page = default_context.new_page()
#page.goto("https://ide.res.zj.hsip.gov.cn", timeout=1000000)

while True:
    #default_context = browser.new_context(ignore_https_errors=True, storage_state="state.json", no_viewport=True)
    page.goto("https://ide.res.zj.hsip.gov.cn", timeout=1000000)
    print('保持')
    page.wait_for_timeout(3600000)
    cookies = default_context.storage_state(path="state.json")
    default_context.clear_cookies()
    default_context.add_cookies(cookies["cookies"])
    #page.goto("https://ide.res.zj.hsip.gov.cn", timeout=1000000)
    #page.wait_for_timeout(3600000)





