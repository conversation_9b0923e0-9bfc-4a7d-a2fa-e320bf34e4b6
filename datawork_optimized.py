
import warnings
import asyncio
import pandas as pd
import time
import dwj
from pathlib import Path
from typing import Dict, List
from functools import wraps
from loguru import logger
from odps import ODPS, options
from numpy import array_split

# 不再使用aiofiles，直接使用同步文件操作

# 配置
warnings.filterwarnings("ignore")
options.verify_ssl = False
logger.add('sql执行.log', rotation="500 MB", encoding='utf-8')

# 常量定义
PAGE_SIZE = 10000  # 每页记录数

# 创建异步包装器，用于将同步函数转换为异步函数
def to_async(func):
    """将同步函数转换为异步函数的装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await asyncio.to_thread(func, *args, **kwargs)
    return wrapper
def limit_sql_query(sql: str, offset: int = 0, limit: int = PAGE_SIZE) -> str:
    """
    为SQL查询添加分页限制

    Args:
        sql: 原始SQL查询语句
        offset: 查询结果的起始偏移量
        limit: 每页返回的最大记录数

    Returns:
        添加了分页限制的SQL查询语句
    """
    sql = sql.strip()
    odber = """ORDER BY BILL_ID,BILL_DETAIL_ID"""
    #odber = """ORDER BY 金额"""
    #odber = """ORDER BY 结算id"""
    #odber = """ORDER BY 单据号"""
    #odber = ""

    if 'with' in sql.lower():
        index = sql.lower().rfind('select')
        return f"{sql[:index]}SELECT * FROM ({sql[index:]})  {odber} LIMIT {offset},{limit}"
    return f"SELECT * FROM ({sql}) {odber} LIMIT {offset},{limit}"

async def execute_odps_sql_async(odps_client, sql):
    """异步执行ODPS SQL查询

    使用run_sql异步执行SQL，然后等待结果并返回reader
    """
    # 使用run_sql异步执行SQL
    instance = odps_client.run_sql(sql)
    # 异步等待实例完成
    await asyncio.to_thread(instance.wait_for_success)
    # 返回reader
    return instance.open_reader(tunnel=True)

async def get_total_count(odps_client, sql: str) -> int:
    """
    异步获取SQL查询的总记录数

    Args:
        odps_client: ODPS客户端对象
        sql: SQL查询语句

    Returns:
        查询结果的总记录数
    """
    count_sql = f"SELECT COUNT(*) AS total_count FROM ({sql})"
    try:
        # 异步执行SQL
        instance = odps_client.run_sql(count_sql)
        # 等待实例完成
        await asyncio.to_thread(instance.wait_for_success)
        # 获取结果
        with instance.open_reader() as reader:
            for record in reader:
                count = record[0]
                break
        logger.info(f"查询总记录数: {count}")
        return count
    except Exception as e:
        logger.error(f"获取总记录数时出错: {str(e)}")
        raise

# 异步写入文件
async def write_to_file(file_path, content, mode='a', encoding='utf-8'):
    """异步写入内容到文件"""
    await asyncio.to_thread(lambda: open(file_path, mode, encoding=encoding).write(content))

async def process_single_sql_concurrently(
    odps_client,
    rule_name: str,
    sql_query: str,
    output_dir: str,
    concurrency: int = 5
) -> List[str]:
    """
    并发处理单条SQL查询

    Args:
        odps_client: ODPS客户端对象
        rule_name: 规则名称
        sql_query: SQL查询语句
        output_dir: 输出目录
        concurrency: 并发任务数量

    Returns:
        生成的文件路径列表
    """
    # 确保输出目录存在
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 获取总记录数
    total_count = await get_total_count(odps_client, sql_query)

    # 记录SQL查询结果数量
    counts_file = Path('logs/sql_result_counts.csv')
    if not counts_file.exists():
        await write_to_file(counts_file, "rule_name,sql,count\n", mode='w')
    await write_to_file(counts_file, f"{rule_name},\"{sql_query}\",{total_count}\n")

    # 如果没有结果，返回空列表
    if total_count == 0:
        logger.warning(f"规则 '{rule_name}' 的SQL查询结果为空")
        return []

    # 如果结果数量小于页面大小，直接执行查询
    if total_count <= PAGE_SIZE:
        logger.info(f"规则 '{rule_name}' 的结果数量较小 ({total_count} 条)，直接执行查询")
        paged_sql = limit_sql_query(sql_query, 0)
        reader = await execute_odps_sql_async(odps_client, paged_sql)
        result_df = await asyncio.to_thread(reader.to_pandas)

        # 保存结果到CSV
        file_path = output_path / f"{rule_name}.csv"
        await asyncio.to_thread(result_df.to_csv, file_path, header=True, index=False, encoding='utf-8')

        return [str(file_path)]

    # 计算需要的分页数
    page_count = (total_count + PAGE_SIZE - 1) // PAGE_SIZE
    logger.info(f"规则 '{rule_name}' 的结果数量较大 ({total_count} 条)，将分 {page_count} 页并发查询")
    # 计算每个并发任务处理的页数
    pages_per_task = max(1, (page_count + concurrency - 1) // concurrency)

    # 创建异步任务
    tasks = []
    result_files = []

    for i in range(0, page_count, pages_per_task):
        start_page = i
        end_page = min(i + pages_per_task, page_count)

        async def process_pages(start_page, end_page):
            page_files = []
            for page_num in range(start_page, end_page):
                offset = page_num * PAGE_SIZE
                limit = min(PAGE_SIZE, total_count - offset)

                try:
                    paged_sql = limit_sql_query(sql_query, offset, limit)
                    reader = await execute_odps_sql_async(odps_client, paged_sql)
                    result_df = await asyncio.to_thread(reader.to_pandas)

                    # 保存结果到CSV
                    file_path = output_path / f"{rule_name}_page_{page_num}.csv"
                    await asyncio.to_thread(result_df.to_csv, file_path, header=True, index=False, encoding='utf-8')

                    page_files.append(str(file_path))
                except Exception as e:
                    logger.error(f"处理规则 '{rule_name}' 的第 {page_num} 页时出错: {str(e)}")
            logger.info(f"规则 '{rule_name}' 的第 {start_page} 到 {end_page - 1} 页处理完成")
            return page_files

        tasks.append(process_pages(start_page, end_page))

    # 并发执行所有任务
    page_results = await asyncio.gather(*tasks)

    # 合并结果
    for result in page_results:
        result_files.extend(result)

    return result_files

async def download_odps_table_to_csv(
    odps_access_id: str,
    odps_secret_access_key: str,
    project_name: str,
    endpoint: str,
    output_dir: str,
    rules_df: pd.DataFrame
) -> Dict[str, List[str]]:
    """
    从ODPS数据库异步下载数据到CSV文件

    Args:
        odps_access_id: ODPS访问ID
        odps_secret_access_key: ODPS访问密钥
        project_name: ODPS项目名称
        endpoint: ODPS端点URL
        output_dir: 输出目录路径
        rules_df: 包含规则名称和SQL查询的DataFrame

    Returns:
        规则名称到文件路径列表的映射字典
    """
    # 确保输出目录存在
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 初始化ODPS连接
    odps_client = ODPS(odps_access_id, odps_secret_access_key, project_name, endpoint=endpoint)

    # 用于存储每个规则对应的CSV文件路径
    result_files = {}

    # 记录SQL查询结果数量的文件路径
    counts_file = Path('logs/sql_result_counts.csv')

    # 如果计数文件不存在，创建并添加标题行
    if not counts_file.exists():
        await write_to_file(counts_file, "rule_name,sql,count\n", mode='w')

    for i, row in rules_df.iterrows():
        rule_name = row['rule_name']
        sql_query = row['sql']
        logger.info(f"正在处理第{i + 1}个规则: {rule_name}")
        offset = 0
        page_num = 0
        result_files[rule_name] = []

        # 分页查询数据
        while True:
            try:
                paged_sql = limit_sql_query(sql_query, offset)

                # 执行SQL查询
                reader = await execute_odps_sql_async(odps_client, paged_sql)
                record_count = reader.count

                # 如果是第一页，记录总结果数
                if offset == 0:
                    await write_to_file(
                        counts_file,
                        f"{rule_name},\"{sql_query}\",{record_count}\n"
                    )
                # 如果没有结果，退出循环
                if record_count == 0:
                    if offset == 0:
                        logger.warning(f"规则 '{rule_name}' 的SQL查询结果为空")
                    break

                result_df = await asyncio.to_thread(reader.to_pandas)

                # 确定输出文件路径
                file_path = output_path / (f"{rule_name}.csv" if record_count < PAGE_SIZE and offset == 0 else f"{rule_name}_page_{page_num}.csv")
                # 保存结果到CSV
                await asyncio.to_thread(result_df.to_csv, file_path, header=True, index=False, encoding='utf-8')

                # 记录文件路径
                result_files[rule_name].append(str(file_path))

                # 如果结果少于PAGE_SIZE条，说明已经获取了所有数据
                if record_count < PAGE_SIZE:
                    break

                # 更新偏移量和页码
                offset += PAGE_SIZE
                page_num += 1

            except Exception as e:
                logger.error(f"处理规则 '{rule_name}' 时出错: {str(e)}")
                break

    return result_files

async def process_rules_concurrently(
    rules_df: pd.DataFrame,
    odps_config: Dict[str, str],
    output_dir: str,
    concurrency: int = 5
) -> Dict[str, List[str]]:
    """
    使用异步并发处理规则

    Args:
        rules_df: 包含规则的DataFrame
        odps_config: ODPS配置信息
        output_dir: 输出目录
        concurrency: 并发任务数量

    Returns:
        所有规则的处理结果合并字典
    """
    # 初始化ODPS连接
    odps_client = ODPS(
        odps_config['access_id'],
        odps_config['secret_access_key'],
        odps_config['project_name'],
        endpoint=odps_config['endpoint']
    )

    # 如果只有一条规则，使用单条SQL并发处理
    if len(rules_df) == 1:
        row = rules_df.iloc[0]
        rule_name = row['rule_name']
        sql_query = row['sql']
        logger.info(f"只有一条规则: {rule_name}，将使用并发处理单条SQL")

        result_files = await process_single_sql_concurrently(
            odps_client,
            rule_name,
            sql_query,
            output_dir,
            concurrency
        )

        return {rule_name: result_files}

    # 多条规则时，将DataFrame分割成多个小块
    chunks = array_split(rules_df, concurrency)

    # 创建异步任务
    tasks = []
    for chunk in chunks:
        task = download_odps_table_to_csv(
            odps_config['access_id'],
            odps_config['secret_access_key'],
            odps_config['project_name'],
            odps_config['endpoint'],
            output_dir,
            chunk
        )
        tasks.append(task)

    # 并发执行所有任务
    results = await asyncio.gather(*tasks)

    all_results = {}
    for result in results:
        all_results.update(result)

    return all_results

async def main():

    odps_config =  {
    "access_id": "UPRJ9Qlhi3bnm38j",
    "secret_access_key": "4uUtT41PzFMAPLWaI1cQIsBK0NRqhQ",
    "project_name": "ls_tjjczq_prd",
    "endpoint": "https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api"
    }

    output_dir = 'd:/xzz/427'
    df = pd.read_csv(r'C:\Users\<USER>\Desktop\61.csv', dtype=str, encoding='gbk')
    df = df[~df['sql'].isnull()]
    #df = df[(df['rule_name'] == '头孢克肟') ]
    df = df.iloc[-1:]
    concurrency = 6
    logger.info(f"开始处理 {df.shape[0]} 条规则")
    try:
        results = await process_rules_concurrently(df, odps_config, output_dir, concurrency)
        logger.info(f"所有规则处理完成,耗时: {(time.time() - start_time):.2f}秒")
        dwj.csv_xlsx(results, 'd:/xzz/427/A', split_by_hospital=False)
        return results
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        raise

if __name__ == '__main__':
    start_time = time.time()
    asyncio.run(main())
