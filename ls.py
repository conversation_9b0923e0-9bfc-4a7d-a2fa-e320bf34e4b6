import re
from time import sleep

import numpy as np
from playwright.async_api import async_playwright
import asyncio
import pandas as pd
from loguru import logger
import tempfile


def compress_sql(sql_code):
    # 移除SQL代码中的注释
    compressed_sql = re.sub(r'--.*?\n', '', sql_code)
    compressed_sql = re.sub(r'/\*.*?\*/', '', compressed_sql)
    # 移除SQL代码中的多余空格和换行符
    compressed_sql = re.sub(r'\s+', ' ', compressed_sql)
    # 返回压缩后的SQL代码
    return compressed_sql


def limit_sql_query(sql, j=0):
    if 'with' in sql.lower():
        index = sql.lower().rfind('select')
        new_sql = sql[:index] + 'SELECT * FROM (' + sql[
                                                    index:] + f""" ) ORDER BY 结算单据号,医疗机构名称,患者社会保障号码  limit {j},10000"""

        # ew_sql = sql[:index] + 'SELECT * FROM (' + sql[index:] + f""" ) ORDER BY 医疗机构名称,总金额,医保项目编码  limit {j},10000"""

    else:
        new_sql = f"""SELECT * FROM ({sql}) ORDER BY 结算单据号,医疗机构名称,患者社会保障号码  limit {j},10000"""
        # new_sql = f"""SELECT * FROM ({sql}) ORDER BY 医疗机构名称,总金额,医保项目编码  limit {j},10000"""
    return new_sql


async def copy_sql_query(page, sql_query):
    # 在文本框中粘贴SQL查询
    textarea = page.locator('textarea.inputarea')
    await textarea.click()
    await page.keyboard.press('Control+a')
    #.press("Control+a")
    await textarea.fill(compress_sql(sql_query))


async def run_sql_query(page):
    # 单击“运行”按钮
    """"""
    await     page.locator("[data-tracker-key='toolbar-button-run']").click()
    # 等待执行结果出现
    while True:
        message_title = await  page.wait_for_selector(".next-message-title", timeout=3000000)
        if message_title and await message_title.text_content() == '执行结束':
            sleep(5)
            break


async def download_query_result(page):
    # 等待结果加载并获取行数
    try:
        await page.wait_for_selector(".excel-bar-right", timeout=10000)
    except:
        return -1
    element =  page.locator(".excel-bar-right > span:nth-child(2)")
    text = await element.text_content()
    ts = re.sub("\D", " ", text)
    total_rows = int(ts)
    return total_rows


async def download_result_file(page, output_file_path):
    # 点击“下载”按钮并等待下载完成
    await page.locator(".next-input-control").first.click()
    await page.get_by_role("option", name="UTF-8").click()
    async with page.expect_download() as download_info:
        await page.get_by_role("button", name="下载").click()
    download = await download_info.value
    download.savapath = await download.path()
    await download.save_as(output_file_path)


async def close_query_result(page):
    # 关闭查询结果
    # result = page.locator('li[role="tab"] >> text=结果[1]')
    result = page.locator('.result-tab >> text=结果[1]')
    await result.click()
    right_button = result.locator("../following-sibling::i[1]")
    await right_button.click()
    sleep(2)


async def main(df_rz,num_processes):

    logger.add('多页面', rotation="500 MB")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True, channel='msedge')
        default_context = await browser.new_context(ignore_https_errors=True, storage_state="state.json")
        pages = []
        for _ in range(num_processes):
            page = await default_context.new_page()
            await page.goto("https://ide.res.zj.hsip.gov.cn", timeout=1000000)
            pages.append(page)
        # 定义要运行的函数
        async def my_function(page, df):
            try:
                await page.get_by_role("button", name="关闭").click()
            except:
                print("没有关闭")
            for i, row in df.iterrows():
                # 填写SQL查询并执行它
                logger.info(f"第{i + 1}个正在执行:{row['rule_name']} ")
                if row['sql'].isspace():
                    logger.info(f"{row['rule_name']} sql 为空 ")
                    continue
                await copy_sql_query(page, limit_sql_query(row["sql"]))
                await run_sql_query(page)
                # 等待结果加载并获取行数
                js = await download_query_result(page)
                if js == -1:  # 没有结果不关闭
                    df_rz.loc[i, 'result'] = 'SQL报错'
                    logger.info(f"{row['rule_name']}sql报错?")
                    continue
                elif js == 10000:  # 10000继续查询
                    # j = 900000
                    j = 10000
                    await download_result_file(page, f"{download_path}{row['rule_name']}_page_0.csv")
                    df_rz.loc[i, 'result'] = '超一万'
                    while True:  # 重新查询
                        await close_query_result(page)  # 先关闭结果
                        new_sql = limit_sql_query(row["sql"], j)
                        await copy_sql_query(page, new_sql)
                        await run_sql_query(page)
                        file_path = f"{download_path}{row['rule_name']}_page_{j}.csv"
                        ts = await download_query_result(page)
                        if ts != 10000:
                            df_rz.loc[i, 'result'] = j + ts
                            await download_result_file(page, file_path)
                            break
                        await download_result_file(page, file_path)
                        j += 10000
                elif js == 0:
                    logger.info(f"{row['rule_name']}  ------没有结果")
                    df_rz.loc[i, 'result'] = '没有结果'
                else:  # 其他的直接下载
                    await download_result_file(page, f"{download_path}{row['rule_name']}.csv")
                    df_rz.loc[i, 'result'] = str(js)
                await close_query_result(page)


        # 在每个页面上调用相同的函数
        chunks = np.array_split(df_rz, num_processes)
        tasks = [asyncio.create_task(my_function(page, chunks[i])) for i, page in enumerate(pages)]
        await asyncio.gather(*tasks)
        # await my_function(page1, df[0])
        # 关闭浏览器
        await browser.close()
        logger.info("浏览器关闭")
    df.to_excel(f"result.xlsx", index=False)






if __name__ == '__main__':
    download_path = tempfile.TemporaryDirectory()
    
    
    print(download_path)
    # 将Excel文件读入pandas dataframe中
    # df = pd.read_excel(r'C:\Users\<USER>\Desktop\fly_rule_jm.xlsx', dtype=str)
    #df = pd.read_csv(r'C:\Users\<USER>\Desktop\511.csv', dtype=str, encoding='gbk')
    df = pd.read_excel(r'C:\Users\<USER>\Desktop\fly_rule_jm.xlsx', dtype=str)
    #df = df[df['rule_name'] == 'GZ0033_(神经肌肉电刺激治疗低频脉冲电治疗)限颈肩腰腿痛']
    #df = df.loc[0:2]
    #df = df[~df['sql'].isnull()]
    df.loc[df.shape[0] + 1,'rule_name'] = input("请输入保存名称:")
    df.loc[df.shape[0] + 1,'sql'] = input("请输入sql语句:")

    num_processes = 1 # 设置进程数
    # chunk_size = len(df) // num_processes
    # chunks = [df.iloc[i:i + chunk_size] for i in range(0, len(df), chunk_size)]
    # if len(chunks) > num_processes:
    #     chunks[-2] = pd.concat([chunks[-2], chunks[-1]])
    #     chunks.pop()
    asyncio.run(main(df,num_processes))
