import tkinter as tk
from tkinter import filedialog

def select_files():
    # 打开文件选择对话框，允许多选
    filenames = filedialog.askopenfilenames(
        title="选择文件",
        filetypes=(("所有文件", "*.*"), 
                  ("Python文件", "*.py"),
                  ("Excel文件", "*.xlsx"),
                  ("CSV文件", "*.csv"))
    )
    
    # 清空列表框
    listbox.delete(0, tk.END)
    
    # 将选中的文件添加到列表框中
    for filename in filenames:
        listbox.insert(tk.END, filename)

# 创建主窗口
root = tk.Tk()
root.title("多文件选择器")
root.geometry("500x400")

# 添加选择文件按钮
select_button = tk.Button(root, text="选择文件", command=select_files)
select_button.pack(pady=20)

# 添加列表框显示选中的文件
listbox = tk.Listbox(root, width=60, height=15)
listbox.pack()

# 运行主循环
root.mainloop()
