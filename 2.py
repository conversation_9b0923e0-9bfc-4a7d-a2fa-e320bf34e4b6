import duckdb
import os

path = input("输入文件夹地址：")
con = duckdb.connect()
con.install_extension('spatial')  # 加载execl控制
con.load_extension('spatial')
con.sql("""CREATE TABLE bl (
                            医疗类别 VARCHAR,
                            规则名称 VARCHAR,
                            结算单据号 VARCHAR,
                            患者社会保障号码 VARCHAR,
                            患者姓名 VARCHAR,
                            费用结算时间 VARCHAR,
                            入院时间 VARCHAR,
                            出院时间 VARCHAR)""")
for path, dir_lst, file_lst in os.walk(path):
    for dir_name in file_lst:
        if dir_name.lower().endswith(".xlsx"):
            print(path + '\\' + dir_name)
            sql = f"""CREATE TABLE a AS 
    SELECT * FROM st_read('{os.path.join(path, dir_name)}',open_options = ['HEADERS=FORCE','FIELD_TYPES=STRING'])"""
            con.sql(sql)
            # con.sql(f"""insert into hz SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '汇总B') """)
            # con.sql(f"""insert into hz SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '汇总C') """)
            # 去重没有结算单据号 异常处理
            try:
                con.sql(f"""DELETE  from a where ROWID not in (select MAX(ROWID) from a GROUP BY 单据号)""")
            except:
                pass
            # if row[7] == '1':
            ts = 5
            #     print('直查')
            df = con.execute('select * from a where 1 <> 1').df()
            djh = 'a.单据号' if '单据号' in df.columns else """''"""
            sfz = 'a.证件号码' if '证件号码' in df.columns else """''"""
            sql_gz = f"""insert into bl select a.医疗类别,'{dir_name}',{djh},{sfz},a.姓名,a.费用结算时间,a.入院时间,a.出院时间 from a TABLESAMPLE RESERVOIR({ts})"""
            # print(sql_gz)
            #print({row[0]})
            #print(con.execute(sql_gz).fetchdf())
            con.sql(sql_gz)
            con.sql("""drop table a""")
        # print(f""" COPY (select   * from bl ) to '{os.path.join(path, dir_name[:-8] + '病历.xlsx')}' WITH (FORMAT GDAL, DRIVER 'xlsx')""")
        # print(con.sql("""select   * from bl """))
con.sql(f""" COPY (select   * from bl ) to '{os.path.join(path, dir_name[:-8] + '病历.xlsx')}' WITH (FORMAT GDAL, DRIVER 'xlsx')""")
con.sql("""drop table bl""")
