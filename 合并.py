import os
import shutil
import re
import duckdb
import pandas as pd
import warnings
warnings.filterwarnings('ignore')
def merge_folders(source_folder, dest_folder):
    """
    将源文件夹中与目标文件夹中相同的子文件夹里的文件合并到一个新的文件夹中。
    :param source_folder: 源文件夹路径
    :param dest_folder: 目标文件夹路径
    """
    # 获取源文件夹和目标文件夹中的子文件夹列表
    source_subfolders = [f.path for f in os.scandir(source_folder) if f.is_dir()]
    dest_subfolders = [f.path for f in os.scandir(dest_folder) if f.is_dir()]
    # 循环遍历源文件夹中的子文件夹
    for source_subfolder in source_subfolders:
        # 获取源文件夹中的子文件夹名称
        subfolder_name = os.path.basename(source_subfolder)
        # 如果目标文件夹中也存在相同名称的子文件夹，则将目标文件夹中的文件合并到源文件夹中的子文件夹中
        for dest_subfolder in dest_subfolders:
            if subfolder_name == os.path.basename(dest_subfolder):
                # 循环遍历目标文件夹中的所有文件，将它们复制到源文件夹中的子文件夹中
                for root, dirs, files in os.walk(dest_subfolder):
                    for file in files:
                        dest_file = os.path.join(root, file)
                        source_file = os.path.join(source_subfolder, file)
                        # 复制到文件夹
                        shutil.copy2(dest_file, source_file)
            # 如果不存在相同名称的子文件夹
        if subfolder_name not in [os.path.basename(dest_subfolder) for dest_subfolder in dest_subfolders]:
            print(f"{subfolder_name}")

def rename_and_merge_files(dir_path, new_name = ''):
    #遍历目录中的所有文件
    con = duckdb.connect(database=':memory:')
    con.install_extension('spatial')  # 加载execl控制
    con.load_extension('spatial')
    for filename in os.listdir(dir_path):
        # 使用正则表达式匹配文件名中的数字序列
        print(filename)
        match = re.search('_2023.*.xlsx', filename)
        if match:
            # 将数字序列替换为新名称
            new_filename = filename[:match.start()] + new_name + filename[match.end() - 5:]
            # 构造旧文件路径和新文件路径
            old_path = os.path.join(dir_path, filename)
            new_path = os.path.join(dir_path, new_filename)
            # 重命名文件
            if os.path.exists(new_path):
                # 如果新文件已经存在，则将旧文件内容合并到新文件中
                # df_new = pd.read_excel(new_path,dtype = str)
                # df_old = pd.read_excel(old_path,dtype = str)
                # df_new.rename(columns=lambda x: str.upper(x), inplace=True)
                # df_old.rename(columns=lambda x: str.upper(x), inplace=True)
                # if '开单医师姓名' in df_old.columns:
                #     df_old.rename(columns={'开单医师姓名': '医师姓名'}, inplace=True)
                # if '开单医师姓名' in df_new.columns:
                #     df_new.rename(columns={'开单医师姓名': '医师姓名'}, inplace=True)
                # if '人员类别' in df_old.columns:
                #     df_old.rename(columns={'人员类别': '人员类型'}, inplace=True)
                # if '人员类别' in df_new.columns:
                #     df_new.rename(columns={'人员类别': '人员类型'}, inplace=True)
                # if '_C13' in df_old.columns:
                #     df_old.rename(columns={'_C13': '项目使用日期'}, inplace=True)
                # if '_C13' in df_new.columns:
                #     df_new.rename(columns={'_C13': '项目使用日期'}, inplace=True)
                # if "TO_CHAR(B.项目使用日期,'YYYY-MM-DD')" in df_old.columns:
                #     df_old.rename(columns={"TO_CHAR(B.项目使用日期,'YYYY-MM-DD')": '项目使用日期'}, inplace=True)
                # if "TO_CHAR(B.项目使用日期,'YYYY-MM-DD')" in df_new.columns:
                #     df_new.rename(columns={"TO_CHAR(B.项目使用日期,'YYYY-MM-DD')": '项目使用日期'}, inplace=True)
                # df_merged = pd.concat([df_new, df_old], ignore_index=True)
                # # 将合并后的数据写入新文件中
                # df_merged.to_excel(new_path,index=False)
                # 删除旧文件
                print(old_path,new_path)
                sql = f"""CREATE TABLE a AS 
                SELECT * FROM st_read('{old_path}',open_options = ['HEADERS=FORCE'])"""
                try:
                    con.execute(sql)
                except Exception as e:
                    print(e)
                    os.remove(old_path)
                    con.close()
                    continue
                df = con.execute('select * from a where 1 = 1').description
                flag = 0
                for i in df:
                    if i[0] == '_c13':
                        con.execute(f"""ALTER TABLE a RENAME _c13 TO 项目使用日期""")
                    if i[0] == '住院号':
                        con.execute(f"""ALTER TABLE a DROP 住院号""")
                    if i[0] == '医疗机构名称':
                        flag = 1
                    if i[0] == '结算日期2':
                        con.execute(f"""ALTER TABLE a DROP 结算日期2""")
                if flag == 0:
                    con.execute(f"""ALTER TABLE a ADD 医疗机构名称 varchar DEFAULT '' """ )
                    con.execute(f"""ALTER TABLE a ADD 医疗机构编码 varchar DEFAULT '' """ )
                con.execute(f"""CREATE TABLE b AS   SELECT * FROM st_read('{new_path}',open_options = ['HEADERS=FORCE'])""")
                df = con.execute('select * from b where 1 = 1').description
                for i in df:
                    if i[0] == '_c13':
                        con.execute(f"""ALTER TABLE b RENAME _c13 TO 项目使用日期""")
                    if i[0] == '住院号':
                        con.execute(f"""ALTER TABLE b DROP 住院号""")
                    if i[0] == '结算日期2':
                        con.execute(f"""ALTER TABLE b DROP 结算日期2""")
                #print(old_path, new_path)
                con.execute(f"""INSERT INTO a  SELECT * FROM b""")
                os.remove(old_path)
                os.remove(new_path)
                sql_name = f""" COPY (select DISTINCT * from a) to '{new_path}'  WITH (FORMAT GDAL, DRIVER 'xlsx')"""
                con.sql(sql_name)
                con.sql("""drop table a""")
                con.sql("""drop table b""")
            else:
                os.rename(old_path, new_path)
#rename_and_merge_files(dir_path)

def yd():
    pass

if __name__ == '__main__':
    #con = duckdb.connect(database=':memory:')
    #con.install_extension('spatial')  # 加载execl控制
    #con.load_extension('spatial')
    #source_folder = r'C:\Users\<USER>\Desktop\新建文件夹 (5)\6-7'
    source_folder = input("第一个文件夹：")
    dest_folder = input("第二个文件夹：")
    #rename_and_merge_files(r'C:\Users\<USER>\Desktop\新建文件夹 (5)\6-7')
    #rename_and_merge_files(source_folder,dest_folder)
    #merge_folders(source_folder, dest_folder)
    for root, dirs, files in os.walk(source_folder):
        for dir in dirs:
            rename_and_merge_files(os.path.join(source_folder,dir))
            #for file in os.listdir(os.path.join(source_folder,dir)):
                #print(file)
                #dest_file = os.path.join(source_folder, dir,file)
                #source_file = os.path.join(source_folder, dir[:-1],file.replace('.xlsx','_2023_242.xlsx'))
                #print(dest_file,source_file)
                    # 复制到文件夹
                #shutil.copy2(dest_file, source_file)
    # def traverse_folder(folder):
    #     for file in os.listdir(folder):
    #         file_path = os.path.join(folder, file)
    #          #(file_path)
    #         #print(file)
