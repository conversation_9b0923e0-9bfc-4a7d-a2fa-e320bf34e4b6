import duckdb
import os
con = duckdb.connect(database=':memory:')
con.install_extension('spatial')#加载execl控制
con.load_extension('spatial')
path = r'D:\xzz\423/'
#path = input("输入文件夹地址：")
#df = duckdb.sql('select * from duckdb_extensions();').df()
#print(df)
# 获取目录中所有CSV文件的列表
csv_files = [f for f in os.listdir(path) if f.endswith('.csv')]
# 按前缀将CSV文件进行分组
csv_dict = {}
for csv_file in csv_files:
    if '_page_' in csv_file:
        prefix = csv_file.split('_page_')[0]
    else:
        prefix = os.path.splitext(csv_file)[0]
    if prefix not in csv_dict:
        csv_dict[prefix] = [csv_file]
    else:
        csv_dict[prefix].append(csv_file)
#print(csv_dict)

grouped_dir = os.path.join(path, '1111')
os.makedirs(grouped_dir, exist_ok=True)

#加载数据字典
con.sql(rf"""CREATE TABLE datatable AS 
SELECT * FROM st_read('{os.path.join(os.getcwd(),'数据组-数据字典20220127.xlsx')}', layer='Sheet1')""")
#合并
for prefix in csv_dict:
    csv_files = csv_dict[prefix]
    csv_files_with_path = [path + f for f in csv_files]
    #print(csv_files)
    a = f"""
CREATE table a AS 
select
	*
from
	read_csv_auto({csv_files_with_path},
	all_varchar = TRUE)
   """
    print(a)
    try:
        con.sql(a)
    except Exception as e:
        print(a)
        print(e)
        continue
    #数据字典更改
    df = con.execute('select * from a where 1 = 1').description
    for i in df:
        if i[0] == '险种类型':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'INSUTYPE'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            con.sql(f"""DELETE FROM a where {i[0]} = '离休人员医疗保障'""")
        if i[0] == '人员类型' or i[0] == '人员类别':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'PSN_TYPE'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        if i[0] == '费用类别' or i[0].lower() == '费用类别a'  or i[0].lower() == '费用类别b':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_CHRGITM_TYPE'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        if i[0] == '患者性别':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'GEND'
                             ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        if i[0] == '支付类别' or i[0].lower() == '支付类别b'  or i[0].lower() == '支付类别2':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'CHRGITM_LV'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        if i[0] == '_c13':
            con.execute(f"""ALTER TABLE a RENAME _c13 TO 项目使用日期""")
        if i[0] == '医疗类别' or i[0] == '就诊类型':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_TYPE'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        if i[0] == '特殊人员类型':
            con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'SP_PSN_TYPE'
                        ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")        

              #################################字典结束########################################
    ifname = con.sql("""SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'a' AND column_name = '定点机构名称' """).fetchall()
    #print(ifname[0][0])
    if ifname[0][0] == 0:
        print(f'{prefix}')
    else:
        results = con.sql(f"""select DISTINCT trim(定点机构名称) from a""").fetchall()
            #output_dir = os.path.join(grouped_dir, row[0].strip())
        output_file = os.path.join(grouped_dir, prefix.strip())
            #文件名加上医院名称
            #output_file = os.path.join(output_dir, row[0].strip() + prefix.strip())
            # try:
            #     os.makedirs(output_dir, exist_ok=True)
            # except Exception as e:
            #     print(e)
            #     print(output_dir)
            #     continue
        sql_name = f""" COPY (select   * from a ) to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
            #三目,加上匹配度
            #sql_name = f""" COPY (select   *,ROUND(jaro_similarity(医院项目名称,医保项目名称) * 100 ,2) || '%' AS 匹配度 from a where trim(医疗机构名称) = '{row[0]}') to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""

            #print(sql_name)
        con.sql(sql_name)
    con.sql("""drop table a""")
