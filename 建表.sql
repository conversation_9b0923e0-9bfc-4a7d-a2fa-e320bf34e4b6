create table hz_yb_fymxxx as
select distinct trim(b.fix_blng_admdvs) AREA_CODE, --统筹区划代码
                trim(a.insu_admdvs) AREA_PERSON_CODE, --参保人统筹区代码
                a.FIXMEDINS_CODE MEDICAL_CODE, --定点机构编码
                a.FIXMEDINS_NAME MEDICAL_NAME, --定点机构名称
                a.psn_no SOCIAL_CARD, --社会保障卡号
                b.certno CARD_ID, --证件号码
                b.PSN_NAME PATIENT_NAME, --姓名
                m.hosp_lv, --医院等级
                c.nat_dic_val_name BENEFIT_TYPE, --险种类型
                d.nat_dic_val_name MEDICAL_MODE, --医疗类别
                a.setl_id BILL_ID, --单据号
                a.BKKP_SN BILL_DETAIL_ID, --单据明细号
                m.ipt_otp_no HOSPITAL_ID, --门诊或住院号
                to_DATE(to_char(a.FEE_OCUR_TIME, 'yyyy-mm-dd hh24:mi:ss'),
                        'yyyy-mm-dd hh24:mi:ss') COST_TIME, --费用发生时间
                to_DATE(to_char(b.setl_time, 'yyyy-mm-dd hh24:mi:ss'),
                        'yyyy-mm-dd hh24:mi:ss') clear_time, --费用结算时间
                case
                  when length(trim(a.hilist_code)) = 15 and
                       trim(a.hilist_code) like '%00' then
                   substr(trim(a.hilist_code), 3, 11)
                  when length(trim(a.hilist_code)) = 27 and
                       (trim(a.hilist_code) like '%-%' or
                        trim(a.hilist_code) like '%f%') then
                   substr(trim(a.hilist_code), -11, 11)
                  when length(trim(a.hilist_code)) = 24 and
                       trim(a.hilist_code) like '%f%' then
                   substr(trim(a.hilist_code), -11, 11)
                  when length(trim(a.hilist_code)) = 13 and
                       trim(a.hilist_code) like '%00' then
                   substr(trim(a.hilist_code), 1, 11)
                  when trim(a.hilist_code) like 'f%' then
                   substr(trim(a.hilist_code), 2, 11)
				  when length(trim(a.hilist_code)) = 26 and
                       trim(a.hilist_code) like '%-%' then
				   substr(a.hilist_code,instr(a.hilist_code,'-')+1,100)
                  else
                   DECODE(a.hilist_code,
                          '',
                          trim(a.hilist_code),
                          replace(a.hilist_code, 'f', ''))
                end item_code, --医保目录编码
                trim(a.HILIST_NAME) ITEM_NAME, --医保目录名称
                a.MEDINS_LIST_CODG ITEM_CODE_HOSP, --机构收费项目编码
                a.MEDINS_LIST_NAME ITEM_NAME_HOSP, --机构收费项目名称
                e.nat_dic_val_name CHARGE_TYPE, --收费项目类别
                f.nat_dic_val_name COST_TYPE, --费用类别
                trim(a.pric) UNIT_PRICE, --单价
                a.pric_uplmt_amt MAX_PRICE, --限价
                '' DOSE, ---------帖数
                a.CNT NUM, --数量
                a.det_item_fee_sumamt MONEY, --金额
                a.SELFPAY_PROP PAY_PER_RETIO, --自付比例
                a.inscp_amt MONEY_MEDICAL, --医保范围费用
                a.PRESELFPAY_AMT MONEY_SELF_PAY, --先行自付金额
                a.FULAMT_OWNPAY_AMT MONEY_SELF_OUT, --全自费金额
                a.dosform_name DOSAGE_FORM, --剂型
                a.SPEC SPEC, --规格
                a.HI_NEGO_DRUG_FLAG HI_NEGO_DRUG_FLAG, --医保谈判药品标志
                a.PRODPLAC_TYPE BUS_PRODUCE, --生产企业
                a.PRODNAME PRODNAME, --商品名
                a.PRCU_DRUG_FLAG, --贵重药品标志
                '' IS_RECIPEL, --是否处方药
                a.TCMDRUG_USED_WAY IS_SINGLE, --单复方标志
                a.RX_DRORD_NO RECIPEL_NO, --处方号
                '' WARD, --病区
                nvl(m.ADM_DEPT_NAME, a.ACORD_DEPT_NAME) DEPT_NAME, --入院科室名称
                a.BILG_DEPT_NAME DISCHARGE_DEPT_NAME, --执行科室名称
                a.BILG_DR_CODE DOCTOR_CODE, --医生编码
                a.BILG_DR_NAME DOCTOR_NAME, --医生姓名
                '' DOCTOR_CARD, ---医生身份证号
                nvl(m.ADM_BED, m.wardarea_bed) BED_NO, --床位号
                m.ipt_days HOSPITAL_NUM, --住院天数
                to_DATE(b.begndate, 'yyyy-mm-dd') IN_TIME, --入院时间
                m.dise_no IN_DIAGNOSE_CODE, --入院诊断疾病编码
                m.dise_name IN_DIAGNOSE_NAME, --入院疾病名称
                to_DATE(b.enddate, 'yyyy-mm-dd') OUT_TIME, --出院时间
                n.diag_code OUT_DIAGNOSE_CODE, --出院疾病诊断编码
                n.diag_name OUT_DIAGNOSE_NAME, --出院疾病名称
                g.nat_dic_val_name DISCHARGE_KIND --离院方式
  from quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls a
  join quansheng_tjjczq_prd.dwd_fin_setl_d_ls b
    on a.setl_id = b.setl_id
   and a.mdtrt_id = b.mdtrt_id
   and a.fixmedins_code = b.fixmedins_code
   and b.refd_setl_flag = '0'
    left join quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls m
    on a.mdtrt_id = m.mdtrt_id
   and a.fixmedins_code = m.fixmedins_code
  left join quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a c
    on c.dic_type_code = 'INSUTYPE'
   and b.insutype = c.nat_dic_val_code
  left join quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a d
    on d.dic_type_code = 'MED_TYPE'
   and b.MED_TYPE = d.nat_dic_val_code
  left join quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a e
    on e.dic_type_code = 'MED_CHRGITM_TYPE'
   and a.MED_CHRGITM_TYPE = e.nat_dic_val_code
  left join quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a f
    on f.dic_type_code = 'CHRGITM_LV'
   and a.CHRGITM_LV = f.nat_dic_val_code
  left join quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a g
    on g.dic_type_code = 'DSCG_WAY'
   and m.DSCG_WAY = g.nat_dic_val_code
  left join (select t.setl_id,t.mdtrt_id,WM_CONCAT(',',t.diag_code) diag_code,WM_CONCAT(',',t.diag_name) diag_name
from quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls t
group by t.setl_id,t.mdtrt_id) n
    on a.setl_id = n.setl_id
 where exists (select 1
          from quansheng_tjjczq_prd.dwd_fin_setl_d_ls b
         where a.setl_id = b.setl_id
           and a.mdtrt_id = b.mdtrt_id
           and b.SETL_TYPE = '2'
           and b.INSUTYPE not in ('340', '350', '360'));




CREATE TABLE hz_yb_fyjsxx AS
SELECT  t.INSU_ADMDVS 参保机构医保区划
        ,t.POOLAREA_NO 统筹区编号
        ,t.FIXMEDINS_NAME 定点机构名称
        ,t.FIXMEDINS_CODE 定点机构编码
        ,a.nat_dic_val_name 超起付线医院等级
        ,b.nat_dic_val_name 限价医院等级
        ,t.SETL_ID 结算ID
        ,t.MEDINS_SETL_ID 医药机构结算ID
        ,t.MDTRT_ID 就诊ID
        ,m.ipt_otp_no 门诊或住院号
        ,t.PSN_NO 人员编号
        ,t.PSN_INSU_RLTS_ID 社会保障卡号
        ,t.PSN_NAME 姓名
        ,t.CERTNO 身份证号
        ,c.nat_dic_val_name 人员类别
        ,d.nat_dic_val_name 险种类型
        ,e.nat_dic_val_name 医疗类别
        ,t.AGE 年龄
        ,t.BRDY 出生日期
        ,t.NATY 民族
        ,CASE    WHEN t.GEND='1' THEN '男'
                 WHEN t.GEND='2' THEN '女'
         END 性别
        ,t.YEAR 年度
        ,t.SETL_TIME 结算时间
        ,t.REFD_SETL_FLAG 退费结算标志
        ,t.MEDFEE_SUMAMT 医疗费用总额
        ,t.ACCT_MULAID_PAY 个人账户共济支付金额
        ,t.BALC 余额
        ,t.CASH_PAYAMT 现金支付金额
        ,t.ACCT_PAY 个人账户支出
        ,t.PSN_PAY 个人支付金额
        ,t.FUND_PAY_SUMAMT 基金支付总额
        ,t.MAF_PAY 医疗救助基金支出
        ,t.HIFDM_PAY 伤残人员医疗保障基金支出
        ,t.HIFOB_PAY 职工大额医疗费用补助基金支出
        ,t.HIFMI_PAY 居民大病保险资金支出
        ,t.HIFES_PAY 企业补充医疗保险基金
        ,t.CVLSERV_PAY 公务员医疗补助资金支出
        ,t.POOL_PROP_SELFPAY 基本医疗保险统筹基金支付比例
        ,t.HIFP_PAY 基金医疗保险统筹基金支出
        ,t.ACT_PAY_DEDC 实际支付起付线
        ,t.INSCP_AMT 符合政策范围内金额
        ,t.PRESELFPAY_AMT 先行自付金额
        ,t.OVERLMT_SELFPAY 超限价自费费用
        ,t.FULAMT_OWNPAY_AMT 全自费金额
        ,f.nat_dic_val_name 个人结算方式
        ,g.nat_dic_val_name 清算方式
        ,h.nat_dic_val_name 清算类别
        ,i.nat_dic_val_name 医保结算类型
        ,l.nat_dic_val_name 就诊凭证类型
        ,a1.chfpdr_name 主诊医师名称
        ,a1.adm_dept_name 入院科室名称
        ,t.BEGNDATE 开始时间
        ,t.ENDDATE 结束时间
        ,CASE    WHEN t.PAY_LOC='1' THEN '中心报销'
                 WHEN t.PAY_LOC='2' THEN '医疗机构'
                 WHEN t.PAY_LOC='3' THEN '省内异地'
                 WHEN t.PAY_LOC='4' THEN '跨省异地'
                 WHEN t.PAY_LOC='5' THEN '互联网医院'
                 WHEN t.PAY_LOC='6' THEN '中心附属医疗机构'
                 WHEN t.PAY_LOC='9' THEN '其他'
                 WHEN t.PAY_LOC='2005' THEN '卫生室5G物联网结算'
         END 支付地点类别
        ,j.nat_dic_val_name 单位类型
        ,t.EMP_NAME 单位名称
        ,CASE    WHEN t.NWB_FLAG='1' THEN '是'
                 WHEN t.NWB_FLAG='0' THEN '否'
         END 新生儿标志
        ,CASE    WHEN t.FLXEMPE_FLAG='1' THEN '是'
                 WHEN t.FLXEMPE_FLAG='0' THEN '否'
         END 灵活就业标志
        ,k.nat_dic_val_name 特殊人员类型
        ,n.nat_dic_val_name 公务员等级
        ,CASE    WHEN t.CVLSERV_FLAG='1' THEN '是'
                 WHEN t.CVLSERV_FLAG='0' THEN '否'
         END 公务员标志
        ,P.oprn_oprt_code 手术操作编码
        ,P.oprn_oprt_name 手术操作名称
        ,COALESCE(o1.diag_code,m.dise_no) 主诊断编码
        ,COALESCE(o1.diag_name,m.dise_name) 主诊断名称
        ,o.diag_code 疾病诊断编码
        ,o.diag_name 疾病诊断名称
FROM    quansheng_tjjczq_prd.dwd_fin_setl_d_ls t
LEFT JOIN quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls m
ON      t.mdtrt_id = m.mdtrt_id
AND     t.fixmedins_code = m.fixmedins_code
AND     t.psn_no = m.psn_no LEFT
JOIN    quansheng_tjjczq_prd.dwd_fin_mdcs_fund_setl_list_ext_d_ls a1
ON      t.fixmedins_code = a1.fixmedins_code
AND     t.SETL_ID = a1.setl_id
AND     a1.vali_flag = '1'
LEFT JOIN quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls o1
ON      t.setl_id = o1.setl_id
AND     t.mdtrt_id = o1.mdtrt_id
AND     o1.vali_flag = '1'
AND     o1.MAINDIAG_FLAG = '1' LEFT
JOIN    (
            SELECT  t.setl_id
                    ,t.mdtrt_id
                    ,WM_CONCAT(',',t.diag_code) diag_code
                    ,WM_CONCAT(',',t.diag_name) diag_name
            FROM    quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls t
            WHERE   t.vali_flag = '1'
            GROUP BY t.setl_id
                     ,t.mdtrt_id
        ) o
ON      t.setl_id = o.setl_id
AND     t.mdtrt_id = o.mdtrt_id
LEFT JOIN (
              SELECT  t.setl_id
                      ,t.mdtrt_id
                      ,WM_CONCAT(',',t.oprn_oprt_code) oprn_oprt_code
                      ,WM_CONCAT(',',t.oprn_oprt_name) oprn_oprt_name
              FROM    quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_oprn_d_ls t
              WHERE   t.vali_flag = '1'
              GROUP BY t.setl_id
                       ,t.mdtrt_id
          ) P
ON      t.setl_id = P.setl_id
AND     t.mdtrt_id = P.mdtrt_id LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A a
ON      a.dic_type_code = 'DEDC_HOSP_LV'
AND     t.dedc_hosp_lv = a.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A b
ON      b.dic_type_code = 'LMTPRIC_HOSP_LV'
AND     t.lmtpric_hosp_lv = b.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A c
ON      c.dic_type_code = 'PSN_TYPE'
AND     t.psn_type = c.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A d
ON      d.dic_type_code = 'INSUTYPE'
AND     t.insutype = d.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A e
ON      e.dic_type_code = 'MED_TYPE'
AND     t.med_type = e.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A f
ON      f.dic_type_code = 'PSN_SETLWAY'
AND     t.PSN_SETLWAY = f.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A g
ON      g.dic_type_code = 'CLR_WAY'
AND     t.CLR_WAY = g.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A h
ON      h.dic_type_code = 'CLR_TYPE'
AND     t.CLR_TYPE = h.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A i
ON      i.dic_type_code = 'SETL_TYPE'
AND     t.SETL_TYPE = i.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A j
ON      j.dic_type_code = 'EMP_TYPE'
AND     t.EMP_TYPE = j.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A k
ON      k.dic_type_code = 'SP_PSN_TYPE'
AND     t.SP_PSN_TYPE = k.nat_dic_val_code
LEFT JOIN quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A l
ON      l.dic_type_code = 'MDTRT_CERT_TYPE'
AND     t.MDTRT_CERT_TYPE = l.nat_dic_val_code LEFT
JOIN    quansheng_tjjczq_prd.DWD_PRD_NAT_DATA_DIC_A n
ON      n.dic_type_code = 'CVLSERV_LV'
AND     t.CVLSERV_LV = n.nat_dic_val_code
WHERE   t.vali_flag = '1'
AND     t.INIT_SETL_ID IS NULL
AND     NOT EXISTS(SELECT 1 FROM quansheng_tjjczq_prd.dwd_fin_setl_d_ls tf WHERE t.setl_id = tf.INIT_SETL_ID AND tf.INIT_SETL_ID IS NOT NULL)
AND     to_char(t.SETL_TIME, 'YYYY') IN ('2022','2023','2024','2025')
;

























SELECT
       DISTINCT
       a.insu_admdvs AREA_CODE,
       a.insu_admdvs AREA_PERSON_CODE,
       a.FIXMEDINS_CODE MEDICAL_CODE,
       a.FIXMEDINS_NAME MEDICAL_NAME,
       a.psn_no SOCIAL_CARD,
       b.certno CARD_ID,
       b.PSN_NAME PATIENT_NAME,
       m.hosp_lv,
       DICT_FIND('INSUTYPE', b.insutype) BENEFIT_TYPE,
       DICT_FIND('MED_TYPE', b.MED_TYPE) MEDICAL_MODE,
       a.setl_id BILL_ID,
       a.BKKP_SN BILL_DETAIL_ID,
       m.ipt_otp_no HOSPITAL_ID,
       CAST(a.FEE_OCUR_TIME AS DATE) COST_TIME,
       CAST(b.setl_time AS DATE) CLEAR_TIME,
       CASE
           WHEN LENGTH(a.hilist_code) = 15 AND SUBSTR(a.hilist_code, -2) = '00'
               THEN SUBSTR(a.hilist_code, 3, 11)
           WHEN LENGTH(a.hilist_code) = 27 AND (INSTR(a.hilist_code, '-') > 0 OR INSTR(a.hilist_code, 'f') > 0)
               THEN SUBSTR(a.hilist_code, -11, 11)
           WHEN LENGTH(a.hilist_code) = 24 AND INSTR(a.hilist_code, 'f') > 0
               THEN SUBSTR(a.hilist_code, -11, 11)
           WHEN LENGTH(a.hilist_code) = 13 AND SUBSTR(a.hilist_code, -2) = '00'
               THEN SUBSTR(a.hilist_code, 1, 11)
           WHEN SUBSTR(a.hilist_code, 1, 1) = 'f'
               THEN SUBSTR(a.hilist_code, 2, 11)
           WHEN LENGTH(a.hilist_code) = 26 AND INSTR(a.hilist_code, '-') > 0
               THEN SUBSTR(a.hilist_code, INSTR(a.hilist_code,'-') + 1)
           ELSE DECODE(a.hilist_code, '', a.hilist_code, REPLACE(a.hilist_code, 'f', ''))
       END item_code,
       a.HILIST_NAME ITEM_NAME,
       a.MEDINS_LIST_CODG ITEM_CODE_HOSP,
       a.MEDINS_LIST_NAME ITEM_NAME_HOSP,
       DICT_FIND('MED_CHRGITM_TYPE', a.MED_CHRGITM_TYPE) CHARGE_TYPE,
       DICT_FIND('CHRGITM_LV', a.CHRGITM_LV) COST_TYPE,
       a.pric UNIT_PRICE,
       a.pric_uplmt_amt MAX_PRICE,
       '' DOSE,
       a.CNT NUM,
       a.det_item_fee_sumamt MONEY,
       a.SELFPAY_PROP PAY_PER_RETIO,
       a.inscp_amt MONEY_MEDICAL,
       a.PRESELFPAY_AMT MONEY_SELF_PAY,
       a.FULAMT_OWNPAY_AMT MONEY_SELF_OUT,
       a.dosform_name DOSAGE_FORM,
       a.SPEC SPEC,
       a.HI_NEGO_DRUG_FLAG HI_NEGO_DRUG_FLAG,
       a.PRODPLAC_TYPE BUS_PRODUCE,
       a.PRODNAME PRODNAME,
       a.PRCU_DRUG_FLAG,
       '' IS_RECIPEL,
       a.TCMDRUG_USED_WAY IS_SINGLE,
       a.RX_DRORD_NO RECIPEL_NO,
       '' WARD,
       NVL(m.ADM_DEPT_NAME, a.ACORD_DEPT_NAME) DEPT_NAME,
       a.BILG_DEPT_NAME DISCHARGE_DEPT_NAME,
       a.BILG_DR_CODE DOCTOR_CODE,
       a.BILG_DR_NAME DOCTOR_NAME,
       '' DOCTOR_CARD,
       NVL(m.ADM_BED, m.wardarea_bed) BED_NO,
       m.ipt_days HOSPITAL_NUM,
       CAST(b.begndate AS DATE) IN_TIME,
       m.ADM_DEPT_CODG IN_DIAGNOSE_CODE,
       m.MAIN_COND_DSCR IN_DIAGNOSE_NAME,
       CAST(b.enddate AS DATE) OUT_TIME,
       n.diag_code OUT_DIAGNOSE_CODE,
       n.diag_name OUT_DIAGNOSE_NAME,
       DICT_FIND('DSCG_WAY', m.DSCG_WAY) DISCHARGE_KIND
FROM quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls a
JOIN quansheng_tjjczq_prd.dwd_fin_setl_d_ls b
    ON a.setl_id = b.setl_id
    AND a.mdtrt_id = b.mdtrt_id
    AND a.fixmedins_code = b.fixmedins_code
    AND b.refd_setl_flag = '0'
    AND b.SETL_TYPE = '2'
    AND b.INSUTYPE NOT IN ('340', '350', '360')
LEFT JOIN quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls m
    ON a.mdtrt_id = m.mdtrt_id
    AND a.fixmedins_code = m.fixmedins_code
LEFT JOIN (
    SELECT t.setl_id,
           t.mdtrt_id,
           LISTAGG(t.diag_code, ',') WITHIN GROUP (ORDER BY t.diag_code) AS diag_code,
           LISTAGG(t.diag_name, ',') WITHIN GROUP (ORDER BY t.diag_name) AS diag_name
    FROM quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls t
    GROUP BY t.setl_id, t.mdtrt_id
) n ON a.setl_id = n.setl_id;






SELECT
       DISTINCT
       a.insu_admdvs AREA_CODE,                    -- 参保地行政区划代码
       a.insu_admdvs AREA_PERSON_CODE,             -- 人员所属行政区划代码
       a.FIXMEDINS_CODE MEDICAL_CODE,              -- 医疗机构编码
       a.FIXMEDINS_NAME MEDICAL_NAME,              -- 医疗机构名称
       a.psn_no SOCIAL_CARD,                       -- 社会保障号码
       b.certno CARD_ID,                           -- 身份证号
       b.PSN_NAME PATIENT_NAME,                    -- 患者姓名
       m.hosp_lv,                                  -- 医院等级
       DICT_FIND('INSUTYPE', b.insutype) BENEFIT_TYPE,  -- 险种类型
       DICT_FIND('MED_TYPE', b.MED_TYPE) MEDICAL_MODE,  -- 医疗类别
       a.setl_id BILL_ID,                          -- 结算ID
       a.BKKP_SN BILL_DETAIL_ID,                   -- 费用明细流水号
       m.ipt_otp_no HOSPITAL_ID,                   -- 住院号
       CAST(a.FEE_OCUR_TIME AS DATE) COST_TIME,    -- 费用发生时间
       CAST(b.setl_time AS DATE) CLEAR_TIME,       -- 结算时间
       CASE
           WHEN LENGTH(a.hilist_code) = 15 AND SUBSTR(a.hilist_code, -2) = '00'
               THEN SUBSTR(a.hilist_code, 3, 11)
           WHEN LENGTH(a.hilist_code) = 27 AND (INSTR(a.hilist_code, '-') > 0 OR INSTR(a.hilist_code, 'f') > 0)
               THEN SUBSTR(a.hilist_code, -11, 11)
           WHEN LENGTH(a.hilist_code) = 24 AND INSTR(a.hilist_code, 'f') > 0
               THEN SUBSTR(a.hilist_code, -11, 11)
           WHEN LENGTH(a.hilist_code) = 13 AND SUBSTR(a.hilist_code, -2) = '00'
               THEN SUBSTR(a.hilist_code, 1, 11)
           WHEN SUBSTR(a.hilist_code, 1, 1) = 'f'
               THEN SUBSTR(a.hilist_code, 2, 11)
           WHEN LENGTH(a.hilist_code) = 26 AND INSTR(a.hilist_code, '-') > 0
               THEN SUBSTR(a.hilist_code, INSTR(a.hilist_code,'-') + 1)
           ELSE DECODE(a.hilist_code, '', a.hilist_code, REPLACE(a.hilist_code, 'f', ''))
       END item_code,                              -- 医保项目编码
       a.HILIST_NAME ITEM_NAME,                    -- 医保项目名称
       a.MEDINS_LIST_CODG ITEM_CODE_HOSP,         -- 医院项目编码
       a.MEDINS_LIST_NAME ITEM_NAME_HOSP,         -- 医院项目名称
       DICT_FIND('MED_CHRGITM_TYPE', a.MED_CHRGITM_TYPE) CHARGE_TYPE,  -- 收费项目等级
       DICT_FIND('CHRGITM_LV', a.CHRGITM_LV) COST_TYPE,  -- 收费项目类别
       a.pric UNIT_PRICE,                          -- 单价
       a.pric_uplmt_amt MAX_PRICE,                 -- 限价金额
       '' DOSE,                                    -- 用量
       a.CNT NUM,                                  -- 数量
       a.det_item_fee_sumamt MONEY,                -- 金额
       a.SELFPAY_PROP PAY_PER_RETIO,              -- 自付比例
       a.inscp_amt MONEY_MEDICAL,                  -- 医保范围内金额
       a.PRESELFPAY_AMT MONEY_SELF_PAY,           -- 个人自付金额
       a.FULAMT_OWNPAY_AMT MONEY_SELF_OUT,        -- 全自费金额
       a.dosform_name DOSAGE_FORM,                 -- 剂型
       a.SPEC SPEC,                                -- 规格
       a.HI_NEGO_DRUG_FLAG HI_NEGO_DRUG_FLAG,     -- 医保谈判药品标志
       a.PRODPLAC_TYPE BUS_PRODUCE,                -- 生产地类别
       a.PRODNAME PRODNAME,                        -- 生产企业名称
       a.PRCU_DRUG_FLAG,                           -- 基本/非基本药物标志
       '' IS_RECIPEL,                              -- 是否处方药
       a.TCMDRUG_USED_WAY IS_SINGLE,              -- 中药使用方式
       a.RX_DRORD_NO RECIPEL_NO,                   -- 处方号
       '' WARD,                                    -- 病区
       NVL(m.ADM_DEPT_NAME, a.ACORD_DEPT_NAME) DEPT_NAME,  -- 科室名称
       a.BILG_DEPT_NAME DISCHARGE_DEPT_NAME,       -- 开单科室名称
       a.BILG_DR_CODE DOCTOR_CODE,                 -- 开单医生代码
       a.BILG_DR_NAME DOCTOR_NAME,                 -- 开单医生姓名
       '' DOCTOR_CARD,                             -- 医师身份证号
       NVL(m.ADM_BED, m.wardarea_bed) BED_NO,     -- 床位号
       m.ipt_days HOSPITAL_NUM,                    -- 住院天数
       CAST(b.begndate AS DATE) IN_TIME,           -- 入院时间
       m.ADM_DEPT_CODG IN_DIAGNOSE_CODE,          -- 入院诊断编码
       m.MAIN_COND_DSCR IN_DIAGNOSE_NAME,         -- 入院诊断名称
       CAST(b.enddate AS DATE) OUT_TIME,           -- 出院时间
       n.diag_code OUT_DIAGNOSE_CODE,              -- 出院诊断编码
       n.diag_name OUT_DIAGNOSE_NAME,              -- 出院诊断名称
       DICT_FIND('DSCG_WAY', m.DSCG_WAY) DISCHARGE_KIND  -- 离院方式
FROM (SELECT * from quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls WHERE setl_id = '330000166847745254300757515053') a    -- 费用明细表
JOIN quansheng_tjjczq_prd.dwd_fin_setl_d_ls b        -- 结算信息表
    ON a.setl_id = b.setl_id
    AND a.mdtrt_id = b.mdtrt_id
    AND a.fixmedins_code = b.fixmedins_code
    AND b.refd_setl_flag = '0'                       -- 非退费结算标志
    AND b.SETL_TYPE = '2'                            -- 结算类型
    AND b.INSUTYPE NOT IN ('340', '350', '360')      -- 排除特定险种
LEFT JOIN quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls m  -- 就医信息表
    ON a.mdtrt_id = m.mdtrt_id
    AND a.fixmedins_code = m.fixmedins_code
LEFT JOIN (
    SELECT t.setl_id,
           t.mdtrt_id,
           WM_CONCAT(',',t.diag_code) AS diag_code,  -- 诊断编码列表
           WM_CONCAT(',',t.diag_code)  AS diag_name   -- 诊断名称列表
    FROM quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls t  -- 诊断信息表
    GROUP BY t.setl_id, t.mdtrt_id
) n ON a.setl_id = n.setl_id


select  身份证号,count(1),sum(医疗费用总额) from hz_yb_fyjsxx where 定点机构名称 = '景宁仁和平价大药房' AND YEAR(结算时间) in (2023,2024)
group by 身份证号
ORDER BY sum(医疗费用总额) desc


SELECT * FROM hz_yb_fymxxx WHERE card_id IN ('332529196007164928',
'332529195710230013',
'332529196808267046',
'33252919750804702X',
'332529196208060017') AND MEDICAL_NAME  = '景宁仁和平价大药房' AND YEAR(CLEAR_TIME)  in (2023,2024)


select distinct 医疗机构名称,医疗机构编码 from jszd2 where 医疗机构名称 like '%湖山%卫生院%'

select min(结算日期) from jszd2 where 医疗机构编码 like 'H33112300064'

 select * from hz_yb_fymxxx where medical_name  like '%湖山%卫生院%' AND item_name like '%针%'








