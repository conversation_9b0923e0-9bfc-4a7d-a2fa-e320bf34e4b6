import os

import duckdb

con = duckdb.connect()
con.install_extension('spatial')#加载execl控制
con.load_extension('spatial')



def cf(path):
    csv_files = [os.path.join(path, f) for f in os.listdir(path) if f.endswith('.csv')]
    a = f"""
    CREATE table a AS 
    select
    	*
    from
    	read_csv_auto({csv_files},
    	all_varchar = TRUE)"""
    print(a)
    con.sql(a)

if __name__ == '__main__':
    path = input("输入文件夹地址：")
    #判断地址最后面是否带\
    if path[-1] != "\\":
        path = path + "\\"
    cf(path)
    input("输入任意键退出：")
