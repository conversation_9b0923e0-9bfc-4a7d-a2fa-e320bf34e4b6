import duckdb
import os
def csv_xlsx(file_path):
    # 连接到内存数据库
    con = duckdb.connect(database=':memory:')
    con.install_extension('spatial')  # 加载空间扩展
    con.load_extension('spatial')

    # 获取用户输入的文件地址
    #file_path = input("请输入文件地址：")

    # 加载数据字典
    con.sql(rf"""CREATE TABLE datatable AS 
    SELECT * FROM st_read('{os.path.join(os.getcwd(), '数据组-数据字典20220127.xlsx')}', layer='Sheet1')""")

    # 创建表以处理用户指定的CSV文件
    a = f"""
    CREATE table a AS 
    select
        *
    from
        read_csv_auto('{file_path}',
        all_varchar = TRUE)
    """
    print(a)
    try:
        con.sql(a)
    except Exception as e:
        print(a)
        print(e)

    # 对数据进行处理
    df = con.execute('select * from a where 1 = 1').description
    for i in df:
            if i[0] == '险种类型':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'INSUTYPE'
                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
                con.sql(f"""DELETE FROM a where {i[0]} = '离休人员医疗保障'""")
            if i[0] == '人员类型' or i[0] == '人员类别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'PSN_TYPE'
                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '费用类别' or i[0] == '费用类别a'  or i[0] == '费用类别b':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_CHRGITM_TYPE'
                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '患者性别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'GEND'
                                ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '支付类别' or i[0] == '支付类别b'  or i[0] == '支付类别2':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'CHRGITM_LV'
                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '_c13':
                con.execute(f"""ALTER TABLE a RENAME _c13 TO 项目使用日期""")
            if i[0] == '医疗类别' or i[0] == '就诊类型':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_TYPE'
                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
    # 保存处理后的数据到新的文件中
    output_file = file_path.replace('.csv', '')
    sql_name = f""" COPY (select   * from a ) to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
    con.sql(sql_name)
    # 关闭数据库连接
    con.close()
