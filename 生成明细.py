import pandas as pd
def inser_where(val,column,Condition = 'OR '): #添加sql条件
   print(val)
   if not val:
       return ''
   trantab = val.maketrans('()（）', '%%%%')#替换括号
   val = val.translate(trantab)
   str1 =  val.split(',')
   ret = " and ("
   print(str1)
   for ide,s in enumerate(str1):
      ret += column +  " like '" +  s + "' "
      if ide < len(str1) - 1:
         ret += Condition + '\n'
   ret += ")"
   print(ret)
   return ret
df = pd.read_excel(r'C:\Users\<USER>\Desktop\lq\明细.xlsx', keep_default_na=False)

sql = """SELECT

 zd.医疗机构编码,
 zd.医疗机构名称,
 zd.结算单据号,
 zd.患者社会保障号码,
 zd.个人编码,
 zd.患者姓名,
 zd.入院日期,
 zd.出院日期,
 zd.结算日期,
 zd.险种类型,
 zd.人员类型,
 mx.费用类别,
 mx.支付类别,
 zd.入院科室,
 zd.出院科室,
zd.主要诊断名称,
  zd.其他诊断名称,
   mx.项目使用日期,
 mx.医保项目编码,
 mx.医保项目名称,
 mx.医院项目编码,
 mx.医院项目名称,
 zd.医疗总费用,
 zd.个人账户支付,
 zd.个人现金支付,
 zd.基本统筹支付,
 mx.报销比例,
 mx.剂型,
 mx.规格,
 mx.单价,
 sum (mx.医保范围内金额) as 医保范围内金额,
 sum (mx.数量) 总数量,
 sum (mx.金额) 总金额
FROM
 jszd2 zd
 JOIN jsmx2 mx ON mx.结算单据号 = zd.结算单据号 and zd.医疗机构编码 IN ('H33110200380','H33119900779','H33110200424')
  where zd.患者社会保障号码 in (select distinct sfz from temp_sfz where dq = '912数据' ) {}
 GROUP BY
 zd.医疗机构编码,
 zd.医疗机构名称,
 zd.结算单据号,
 zd.患者社会保障号码,
 zd.个人编码,
 zd.患者姓名,
 zd.入院日期,
 zd.出院日期,
 zd.结算日期,
 zd.险种类型,
 zd.人员类型,
 mx.费用类别,
 mx.支付类别,
 zd.入院科室,
 zd.出院科室,
zd.主要诊断名称,
 zd.其他诊断名称,
    mx.项目使用日期,
 mx.医保项目编码,
 mx.医保项目名称,
 mx.医院项目编码,
 mx.医院项目名称,
 zd.医疗总费用,
 zd.个人账户支付,
 zd.个人现金支付,
 zd.基本统筹支付,
 mx.报销比例,
 mx.剂型,
 mx.规格,
 mx.单价
 HAVING
 SUM(数量) > 0

"""
df2 = pd.DataFrame(columns=['rule_name','sql'])
for index, row in df.iterrows():
    NEW_sql = sql.format(inser_where(row['医保项目名称'], 'mx.医保项目名称') + inser_where(row['诊断'], 'zd.主要诊断名称 not',Condition='AND') + f" and to_char(zd.结算日期,'yyyy-mm-dd') < '{row['结算日期']}'")
    df2.loc[index,'sql'] = NEW_sql
    df2.loc[index,'rule_name'] = row['医保项目名称']
df2.to_excel(r'C:\Users\<USER>\Desktop\lq\明细sql.xlsx',index=False)