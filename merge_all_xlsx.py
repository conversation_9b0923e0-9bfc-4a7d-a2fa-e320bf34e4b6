from openpyxl import load_workbook, Workbook
import os
from datetime import datetime

def merge_xlsx_files():
    # 获取用户输入的文件夹路径
    folder_path = input("请输入包含xlsx文件的文件夹路径: ").strip()
    if not os.path.isdir(folder_path):
        print(f"错误: 文件夹路径 '{folder_path}' 不存在或无效")
        return
    
    # 读取规则名称.xlsx文件
    rule_file = os.path.join( '规则名称.xlsx')
    if not os.path.exists(rule_file):
        print(f"未找到规则名称.xlsx文件在路径: {folder_path}")
        return
    
    rule_wb = load_workbook(rule_file)
    rule_sheet = rule_wb.active
    
    # 获取要处理的xlsx文件名列表(第一列)
    xlsx_files = []
    for row in rule_sheet.iter_rows(min_row=2, max_col=1, values_only=True):
        if row[0] and isinstance(row[0], str) and row[0].endswith('.xlsx'):
            file_path = os.path.join(folder_path, row[0])
            xlsx_files.append(file_path)
    
    if not xlsx_files:
        print("规则文件中未找到有效的xlsx文件名")
        return
    
    # 创建新的工作簿
    merged_wb = Workbook()
    # 删除默认创建的工作表
    del merged_wb[merged_wb.sheetnames[0]]
    
    # 创建汇总工作表(放在最前面)
    summary_sheet = merged_wb.create_sheet("汇总", 0)
    summary_sheet.append(["文件名", "工作表名", "最后一列数据"])  # 添加表头
    
    # 遍历所有要处理的xlsx文件
    for file in xlsx_files:
        if not os.path.exists(file):
            print(f"文件 {file} 不存在，跳过")
            continue
            
        # 加载工作簿
        wb = load_workbook(file)
        file_name = os.path.basename(file)  # 只获取文件名部分
        sheet_counter = 1
        
        # 处理每个工作表
        for sheet in wb.sheetnames:
            final_sheet_name = str(sheet_counter)
            sheet_counter += 1
            
            # 创建工作表并复制内容
            source_sheet = wb[sheet]
            new_sheet = merged_wb.create_sheet(final_sheet_name)
            
            # 复制单元格数据
            last_col_sum = 0
            last_col = source_sheet.max_column
            for row in source_sheet.iter_rows():
                for cell in row:
                    new_sheet[cell.coordinate].value = cell.value
                    if cell.column == last_col and isinstance(cell.value, (int, float)):  # 如果是最后一列且是数字
                        last_col_sum += cell.value
            
            # 添加最后一列的和到汇总表
            summary_sheet.append([file_name, sheet, last_col_sum])
    
    # 保存合并后的文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"merged_{timestamp}.xlsx"
    merged_wb.save(output_file)
    print(f"合并完成，结果保存在: {output_file}")

if __name__ == "__main__":
    merge_xlsx_files()
