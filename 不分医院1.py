import duckdb
import os

# 连接数据库
con = duckdb.connect(database=':memory:')
con.install_extension('spatial')  # 加载扩展
con.load_extension('spatial')

path = r'G:/lq/A/aa/'
grouped_dir = os.path.join(path, '0113')
os.makedirs(grouped_dir, exist_ok=True)

# 加载数据字典
con.sql(rf"""CREATE TABLE datatable AS 
SELECT * FROM st_read('{os.path.join(os.getcwd(), '数据组-数据字典20220127.xlsx')}', layer='Sheet1')""")
#大小写敏感
con.sql("""SET preserve_identifier_case = FALSE """)
def update_table(table_name, column_name, dict_identifier):
    """更新表中的指定列为数据字典中的名称，不使用临时表和 WHERE EXISTS"""

    # 直接在更新语句中进行联接
    con.execute(f"""
        UPDATE {table_name} 
SET "{column_name}" = datatable."字典值名称"
FROM datatable 
WHERE "{table_name}"."{column_name}" = datatable."字典值编码" 
AND datatable."字典标识符" = '{dict_identifier}'
    """)

csv_files = [f for f in os.listdir(path) if f.endswith('.csv')]
csv_dict = {}

# 按前缀将CSV文件分组
for csv_file in csv_files:
    prefix = csv_file.split('_page_')[0] if '_page_' in csv_file else os.path.splitext(csv_file)[0]
    csv_dict.setdefault(prefix, []).append(csv_file)

# 合并CSV文件
for prefix, files in csv_dict.items():
    files_with_path = [os.path.join(path, f) for f in files]
    
    try:
        a = f"""
            CREATE TABLE a AS 
            SELECT * FROM read_csv_auto({files_with_path}, all_varchar = TRUE)
        """
        con.sql(a)
        print(a)
    except Exception as e:
        print(f"处理文件 {prefix} 失败: {e}")
        continue

    # 更新数据字典
    for column_info in con.execute('SELECT * FROM a WHERE 1 = 1').description:
        column_name = column_info[0]
        if column_name == '险种类型':
            update_table('a', column_name, 'INSUTYPE')
            con.sql(f"DELETE FROM a WHERE {column_name} = '离休人员医疗保障'")
        elif column_name in ['人员类型', '人员类别']:
            update_table('a', column_name, 'PSN_TYPE')
        elif column_name in ['费用类别', '费用类别A', '费用类别b']:
            update_table('a', column_name, 'MED_CHRGITM_TYPE')
        elif column_name == '患者性别':
            update_table('a', column_name, 'GEND')
        elif column_name in ['支付类别', '支付类别a', '支付类别2','支付类别b']:
            update_table('a', column_name, 'CHRGITM_LV')
        elif column_name == '_c13':
            con.execute(f"ALTER TABLE a RENAME _c13 TO 项目使用日期")
        elif column_name in ['医疗类别', '就诊类型']:
            update_table('a', column_name, 'MED_TYPE')
        elif column_name == '特殊人员类型':
            update_table('a', column_name, 'SP_PSN_TYPE')

    # 检查指定列是否存在
    if con.sql("""SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'a' AND column_name = '医疗机构名称'""").fetchone()[0] == 0:
        print(f'{prefix} 没有医疗机构名称列')
    else:
        results = con.sql("SELECT DISTINCT TRIM(医疗机构名称) FROM a").fetchall()
        
        for row in results:
            output_file = os.path.join(grouped_dir, prefix.strip())
            sql_name = f"COPY (SELECT * FROM a) TO '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"
            con.sql(sql_name)

    con.sql("DROP TABLE a")
