import re
import subprocess
from datetime import datetime
from time import sleep, time
from multiprocessing import Pool
from playwright.async_api import expect
from playwright.sync_api import sync_playwright
import pandas as pd
import pandas.io.clipboard as cb
from loguru import logger

def compress_sql(sql_code):
    # 移除SQL代码中的注释
    compressed_sql = re.sub(r'--.*?\n', '', sql_code)
    compressed_sql = re.sub(r'/\*.*?\*/', '', compressed_sql)
    # 移除SQL代码中的多余空格和换行符
    compressed_sql = re.sub(r'\s+', ' ', compressed_sql)
    # 返回压缩后的SQL代码
    return compressed_sql
def limit_sql_query(sql, j = 0):
    if 'with' in sql.lower():
        index = sql.lower().rfind('select')
        new_sql = sql[:index] + 'SELECT * FROM (' + sql[index:] + f""" ) ORDER BY 结算单据号,医疗机构名称,患者社会保障号码,医保项目编码  limit {j},10000"""

        #ew_sql = sql[:index] + 'SELECT * FROM (' + sql[index:] + f""" ) ORDER BY 医疗机构名称,总金额,医保项目编码  limit {j},10000"""

    else:
        new_sql = f"""SELECT * FROM ({sql}) ORDER BY 结算单据号,医疗机构名称,患者社会保障号码,医保项目编码  limit {j},10000"""
        #new_sql = f"""SELECT * FROM ({sql}) ORDER BY 医疗机构名称,总金额,医保项目编码  limit {j},10000"""
    return new_sql

def copy_sql_query(page, sql_query):
    # 在文本框中粘贴SQL查询
    textarea = page.locator('textarea.inputarea')
    #cb.copy(sql_query)
    textarea.press("Control+a")
    textarea.fill(compress_sql(sql_query))# 格式不对,只能用黏贴
    #textarea.press("Control+v")

def run_sql_query(page):
    # 单击“运行”按钮
    page.locator("[data-tracker-key='toolbar-button-run']").click()
    # 等待执行结果出现
    while True:
        message_title = page.wait_for_selector(".next-message-title", timeout=3000000)
        if message_title and message_title.text_content() == '执行结束':
            break

def download_query_result(page):
    # 等待结果加载并获取行数
    try:
        page.wait_for_selector(".excel-bar-right", timeout=10000)
    except:
        return -1
    element = page.locator(".excel-bar-right > span:nth-child(2)")
    text = element.text_content()
    ts = re.sub("\D", " ", text)
    total_rows = int(ts)
    return total_rows

def download_result_file(page, output_file_path):
    # 点击“下载”按钮并等待下载完成
    page.locator(".next-input-control").first.click()
    page.get_by_role("option", name="UTF-8").click()
    with page.expect_download() as download_info:
        page.get_by_role("button", name="下载").click()
    download = download_info.value
    download.savapath = download.path()
    #logger.info(f"目录位置:{download.path()}")
    #print()
    download.save_as(output_file_path)

def close_query_result(page):
    # 关闭查询结果
    #result = page.locator('li[role="tab"] >> text=结果[1]')
    result = page.locator('.result-tab >> text=结果[1]')
    result.click()
    right_button = result.locator("../following-sibling::i[1]")
    right_button.click()
    sleep(5)

def process_dataframe(df, log_file_path):
    #logger.remove()  # 取消控制台输出
    logger.add(log_file_path, rotation="500 MB")
    # 使用Playwright连接到浏览器
    playwright = sync_playwright().start()
    browser = playwright.chromium.launch(headless=False,channel='msedge')
    #加载cookies
    default_context = browser.new_context(ignore_https_errors=True, storage_state="auth.json")
    page = default_context.new_page()
    page.goto("https://ide2-cn-hangzhou.data.aliyun.com/", timeout=1000000)
    # try:
    #     page.get_by_role("button", name="关闭").click()
    # except:
    #     print("没有关闭")
    # 循环遍历 DataFrame 中的每一行，填写 SQL 查询并执行它
    for i, row in df.iterrows():
        # 填写SQL查询并执行它
        logger.info(f"第{i + 1}个正在执行:{row['rule_name']} ")
        if row['sql'].isspace():
            logger.info(f"{row['rule_name']} sql 为空 ")
            continue
        copy_sql_query(page, limit_sql_query(row["sql"]))
        run_sql_query(page)
        # 等待结果加载并获取行数
        js = download_query_result(page)
        if js == -1:  # 没有结果不关闭
            df.loc[i, 'result'] = 'SQL报错'
            logger.info(f"{row['rule_name']}sql报错?")
            continue
        elif js == 10000:  # 10000继续查询
            #j = 900000
            j = 10000
            download_result_file(page, f"d:/xzz/{row['rule_name']}_page_0.csv")
            df.loc[i, 'result'] = '超一万'
            while True:  # 重新查询
                close_query_result(page)  # 先关闭结果
                #row["sql"] + f""" limit {j},10000"""
                new_sql = limit_sql_query(row["sql"], j)
                copy_sql_query(page, new_sql)
                run_sql_query(page)
                file_path = f"d:/xzz/{row['rule_name']}_page_{j}.csv"
                if download_query_result(page) != 10000:
                    download_result_file(page, file_path)
                    break
                download_result_file(page, file_path)
                j += 10000
        elif js == 0:
            logger.info(f"{row['rule_name']}  ------没有结果")
            df.loc[i, 'result'] = '没有结果'
        else:  # 其他的直接下载
            download_result_file(page, f"d:/xzz/{row['rule_name']}.csv")
            df.loc[i, 'result'] = str(js)
        close_query_result(page)
    df.to_excel(log_file_path + '.xlsx', index=False)
    default_context.storage_state(path="state.json")
    browser.close()

if __name__ == '__main__':
    # 将Excel文件读入pandas dataframe中
    #df = pd.read_excel(r'C:\Users\<USER>\Desktop\fly_rule_jm.xlsx', dtype=str)
    df = pd.read_csv(r'C:\Users\<USER>\Desktop\65.csv', dtype=str,encoding='gbk')
    #df = df[df['rule_name'] == '康复医院手术耗材']
    df = df[~df['sql'].isnull()]
    #df = df[~df['cp'].isnull()]
    #df = df.loc[89:91]
   # df = df[(df['rule_name'] == '开展（支气管舒张试验）时，同时收取（肺通气功能检查）项目费用。')
    #        | (df['rule_name'] == '开展(24小时动态心电图)时，同时收取(心电监护)项目费用。')
     #       | (df['rule_name'] == '将非过敏性哮喘，荨麻疹，过敏性鼻炎、过敏性肠炎患者的（吸入物变应原筛查）（25040500200）、（食入物变应原筛查）（25040500300）、（特殊变应原（多价变应原）筛查）（25040500400）费用纳入医保结算')
      #      | (df['rule_name'] == '门诊将非过敏性哮喘，荨麻疹，过敏性鼻炎、过敏性肠炎患者的（吸入物变应原筛查）（25040500200）、（食入物变应原筛查）（25040500300）、（特殊变应原（多价变应原）筛查）（25040500400）费用纳入医保结算')
       #     | (df['rule_name'] == '2开展（神经肌肉电刺激治疗）时，同时收取（低频脉冲电治疗）项目费用。')]
    #df = df.loc[77:]
    #df = df[(df['rule_name'] == 'GZ0093_检验套餐(急诊生化常规检查)或(电解质测定)重复收取(钾、钠、氯钙测定)') ]
    #df = df[(df['result'] == 'SQL报错')  ]
    #df = df.loc[1446:]
    # 将DataFrame切分成多个子DataFrame
    num_processes = 1 # 设置进程数
    chunk_size = len(df) // num_processes
    chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
    if len(chunks) > num_processes:
        chunks[-2] = pd.concat([chunks[-2], chunks[-1]])
        chunks.pop()
    # 为每个进程分配一个日志文件
    log_file_paths = [f"sql执行-{i+1}.log" for i in range(num_processes)]
    # 使用进程池并行处理每个子DataFrame
    with Pool(num_processes) as p:
        p.starmap(process_dataframe, zip(chunks, log_file_paths))
