select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1