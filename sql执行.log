2025-04-29 15:41:26.631 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-04-29 15:41:26.700 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 遂昌县湖山中心卫生院，将使用并发处理单条SQL
2025-04-29 15:41:44.275 | INFO     | __main__:get_total_count:86 - 查询总记录数: 45523
2025-04-29 15:41:44.279 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '遂昌县湖山中心卫生院' 的结果数量较大 (45523 条)，将分 5 页并发查询
2025-04-29 15:41:44.688 | ERROR    | __main__:process_pages:180 - 处理规则 '遂昌县湖山中心卫生院' 的第 1 页时出错: ODPS-0130071: InstanceId: 20250429074143526gsqzvr1
ODPS-0130071:[3,41] Semantic analysis exception - column `金额` cannot be resolved

2025-04-29 15:41:44.697 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 1 到 1 页处理完成
2025-04-29 15:41:44.773 | ERROR    | __main__:process_pages:180 - 处理规则 '遂昌县湖山中心卫生院' 的第 2 页时出错: ODPS-0130071: InstanceId: 20250429074143582geevfns1
ODPS-0130071:[3,41] Semantic analysis exception - column `金额` cannot be resolved

2025-04-29 15:41:44.775 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 2 到 2 页处理完成
2025-04-29 15:41:44.852 | ERROR    | __main__:process_pages:180 - 处理规则 '遂昌县湖山中心卫生院' 的第 3 页时出错: ODPS-0130071: InstanceId: 20250429074143673gt81ins1
ODPS-0130071:[3,41] Semantic analysis exception - column `金额` cannot be resolved

2025-04-29 15:41:44.853 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 3 到 3 页处理完成
2025-04-29 15:41:44.879 | ERROR    | __main__:process_pages:180 - 处理规则 '遂昌县湖山中心卫生院' 的第 4 页时出错: ODPS-0130071: InstanceId: 20250429074143730gtqzvr1
ODPS-0130071:[3,41] Semantic analysis exception - column `金额` cannot be resolved

2025-04-29 15:41:44.879 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 4 到 4 页处理完成
2025-04-29 15:41:45.606 | ERROR    | __main__:process_pages:180 - 处理规则 '遂昌县湖山中心卫生院' 的第 0 页时出错: ODPS-0130071: InstanceId: 20250429074143434gkh5kns1
ODPS-0130071:[3,41] Semantic analysis exception - column `金额` cannot be resolved

2025-04-29 15:41:45.607 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 0 到 0 页处理完成
2025-04-29 15:41:45.608 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 18.99秒
2025-04-29 15:41:45.744 | ERROR    | __main__:main:377 - 处理过程中发生错误: Invalid Input Error: Python Object "a" of type "str" found on line "c:\Users\<USER>\Desktop\py\dwj.py:33" not suitable for replacement scans.
Make sure that "a" is either a pandas.DataFrame, duckdb.DuckDBPyRelation, pyarrow Table, Dataset, RecordBatchReader, Scanner, or NumPy ndarrays with supported format
2025-04-29 15:42:31.586 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-04-29 15:42:31.643 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 遂昌县湖山中心卫生院，将使用并发处理单条SQL
2025-04-29 15:42:46.200 | INFO     | __main__:get_total_count:86 - 查询总记录数: 45523
2025-04-29 15:42:46.209 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '遂昌县湖山中心卫生院' 的结果数量较大 (45523 条)，将分 5 页并发查询
2025-04-29 15:44:28.916 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 2 到 2 页处理完成
2025-04-29 15:44:29.646 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 0 到 0 页处理完成
2025-04-29 15:44:31.877 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 4 到 4 页处理完成
2025-04-29 15:44:32.174 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 3 到 3 页处理完成
2025-04-29 15:44:32.748 | INFO     | __main__:process_pages:181 - 规则 '遂昌县湖山中心卫生院' 的第 1 到 1 页处理完成
2025-04-29 15:44:32.755 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 121.17秒
2025-04-29 16:37:14.806 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-04-29 16:37:14.875 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 遂昌县湖山中心卫生院(2023)，将使用并发处理单条SQL
2025-04-29 16:37:52.359 | INFO     | __main__:get_total_count:86 - 查询总记录数: 1316
2025-04-29 16:37:52.361 | INFO     | __main__:process_single_sql_concurrently:137 - 规则 '遂昌县湖山中心卫生院(2023)' 的结果数量较小 (1316 条)，直接执行查询
2025-04-29 16:38:28.512 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 73.72秒
2025-04-30 12:01:25.956 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-04-30 12:01:26.051 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 头孢克肟，将使用并发处理单条SQL
2025-04-30 12:02:02.900 | INFO     | __main__:get_total_count:86 - 查询总记录数: 180
2025-04-30 12:02:02.902 | INFO     | __main__:process_single_sql_concurrently:137 - 规则 '头孢克肟' 的结果数量较小 (180 条)，直接执行查询
2025-04-30 12:04:17.848 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 171.91秒
2025-04-30 16:25:51.060 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-04-30 16:25:51.116 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 金额大于500，将使用并发处理单条SQL
2025-04-30 16:27:51.229 | INFO     | __main__:get_total_count:86 - 查询总记录数: 12267
2025-04-30 16:27:51.231 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '金额大于500' 的结果数量较大 (12267 条)，将分 2 页并发查询
2025-04-30 16:29:53.535 | INFO     | __main__:process_pages:181 - 规则 '金额大于500' 的第 0 到 0 页处理完成
2025-04-30 16:34:34.191 | INFO     | __main__:process_pages:181 - 规则 '金额大于500' 的第 1 到 1 页处理完成
2025-04-30 16:34:34.193 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 523.14秒
2025-05-12 09:44:14.810 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 09:44:14.872 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 脏器灰阶立体成像”和“彩超常规检查（一个部位）”同时收费2，将使用并发处理单条SQL
2025-05-12 09:44:30.595 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 09:44:30.652 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 凝血因子VIII，将使用并发处理单条SQL
2025-05-12 09:44:30.996 | ERROR    | __main__:get_total_count:89 - 获取总记录数时出错: ODPS-0130131: InstanceId: 20250512014430448g75y7hx1
ODPS-0130131:[1,52] Table not found - table ls_tjjczq_prd.hz_yb_fymxxxw cannot be resolved

2025-05-12 09:44:30.997 | ERROR    | __main__:main:377 - 处理过程中发生错误: ODPS-0130131: InstanceId: 20250512014430448g75y7hx1
ODPS-0130131:[1,52] Table not found - table ls_tjjczq_prd.hz_yb_fymxxxw cannot be resolved

2025-05-12 09:46:34.310 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 09:46:34.382 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 凝血因子VIII，将使用并发处理单条SQL
2025-05-12 09:46:50.321 | INFO     | __main__:get_total_count:86 - 查询总记录数: 174
2025-05-12 09:46:50.327 | INFO     | __main__:process_single_sql_concurrently:137 - 规则 '凝血因子VIII' 的结果数量较小 (174 条)，直接执行查询
2025-05-12 09:47:24.586 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 50.28秒
2025-05-12 09:49:26.444 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 09:49:26.669 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 凝血因子VIII，将使用并发处理单条SQL
2025-05-12 09:50:11.038 | INFO     | __main__:get_total_count:86 - 查询总记录数: 244
2025-05-12 09:50:11.042 | INFO     | __main__:process_single_sql_concurrently:137 - 规则 '凝血因子VIII' 的结果数量较小 (244 条)，直接执行查询
2025-05-12 09:50:40.452 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 74.02秒
2025-05-12 09:56:12.081 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 09:56:12.150 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 凝血因子VIII，将使用并发处理单条SQL
2025-05-12 09:57:53.502 | INFO     | __main__:get_total_count:86 - 查询总记录数: 507
2025-05-12 09:57:53.503 | INFO     | __main__:process_single_sql_concurrently:137 - 规则 '凝血因子VIII' 的结果数量较小 (507 条)，直接执行查询
2025-05-12 09:58:36.671 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 144.60秒
2025-05-12 15:02:05.848 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 15:02:05.946 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 浙江启正大药房有限公司遂昌云峰店，将使用并发处理单条SQL
2025-05-12 15:02:31.955 | INFO     | __main__:get_total_count:86 - 查询总记录数: 17484
2025-05-12 15:02:31.958 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '浙江启正大药房有限公司遂昌云峰店' 的结果数量较大 (17484 条)，将分 2 页并发查询
2025-05-12 15:03:48.195 | INFO     | __main__:process_pages:181 - 规则 '浙江启正大药房有限公司遂昌云峰店' 的第 1 到 1 页处理完成
2025-05-12 15:05:07.064 | INFO     | __main__:process_pages:181 - 规则 '浙江启正大药房有限公司遂昌云峰店' 的第 0 到 0 页处理完成
2025-05-12 15:05:07.075 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 181.24秒
2025-05-12 16:33:49.109 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-12 16:33:49.270 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 奥希替尼，将使用并发处理单条SQL
2025-05-12 16:34:20.561 | INFO     | __main__:get_total_count:86 - 查询总记录数: 0
2025-05-12 16:34:20.566 | WARNING  | __main__:process_single_sql_concurrently:132 - 规则 '奥希替尼' 的SQL查询结果为空
2025-05-12 16:34:20.567 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 31.47秒
2025-05-12 16:34:21.296 | ERROR    | __main__:main:377 - 处理过程中发生错误: Invalid Input Error: Python Object "a" of type "str" found on line "c:\Users\<USER>\Desktop\py\dwj.py:33" not suitable for replacement scans.
Make sure that "a" is either a pandas.DataFrame, duckdb.DuckDBPyRelation, pyarrow Table, Dataset, RecordBatchReader, Scanner, or NumPy ndarrays with supported format
2025-05-15 09:44:41.161 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-15 09:44:41.228 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 明细，将使用并发处理单条SQL
2025-05-15 09:45:39.391 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-15 09:45:39.474 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 明细，将使用并发处理单条SQL
2025-05-15 09:46:04.833 | INFO     | __main__:get_total_count:86 - 查询总记录数: 37789
2025-05-15 09:46:04.839 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '明细' 的结果数量较大 (37789 条)，将分 4 页并发查询
2025-05-15 09:47:27.932 | INFO     | __main__:process_pages:181 - 规则 '明细' 的第 1 到 1 页处理完成
2025-05-15 09:50:41.374 | INFO     | __main__:process_pages:181 - 规则 '明细' 的第 2 到 2 页处理完成
2025-05-15 09:50:47.266 | INFO     | __main__:process_pages:181 - 规则 '明细' 的第 0 到 0 页处理完成
2025-05-15 09:50:54.256 | INFO     | __main__:process_pages:181 - 规则 '明细' 的第 3 到 3 页处理完成
2025-05-15 09:50:54.263 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 314.88秒
2025-05-16 16:42:10.433 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-16 16:42:10.490 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 1腰部疾病推拿治疗，将使用并发处理单条SQL
2025-05-16 16:42:46.939 | INFO     | __main__:get_total_count:86 - 查询总记录数: 13928
2025-05-16 16:42:46.942 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '1腰部疾病推拿治疗' 的结果数量较大 (13928 条)，将分 2 页并发查询
2025-05-16 16:44:08.946 | INFO     | __main__:process_pages:181 - 规则 '1腰部疾病推拿治疗' 的第 1 到 1 页处理完成
2025-05-16 16:45:22.401 | INFO     | __main__:process_pages:181 - 规则 '1腰部疾病推拿治疗' 的第 0 到 0 页处理完成
2025-05-16 16:45:22.410 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 191.99秒
2025-05-16 17:06:15.464 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-16 17:06:15.559 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 腰部疾病推拿治疗，将使用并发处理单条SQL
2025-05-16 17:06:37.384 | INFO     | __main__:get_total_count:86 - 查询总记录数: 157878
2025-05-16 17:06:37.390 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '腰部疾病推拿治疗' 的结果数量较大 (157878 条)，将分 16 页并发查询
2025-05-16 17:13:33.182 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 15 到 15 页处理完成
2025-05-16 17:20:24.036 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 3 到 5 页处理完成
2025-05-16 17:23:42.847 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 6 到 8 页处理完成
2025-05-16 17:24:06.221 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 0 到 2 页处理完成
2025-05-16 17:24:20.782 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 9 到 11 页处理完成
2025-05-16 17:24:24.691 | INFO     | __main__:process_pages:181 - 规则 '腰部疾病推拿治疗' 的第 12 到 14 页处理完成
2025-05-16 17:24:24.700 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 1089.24秒
2025-05-19 08:32:27.614 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 08:32:27.688 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 缙云鸿福康复医院，将使用并发处理单条SQL
2025-05-19 08:32:44.243 | INFO     | __main__:get_total_count:86 - 查询总记录数: 1494115
2025-05-19 08:32:44.246 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '缙云鸿福康复医院' 的结果数量较大 (1494115 条)，将分 150 页并发查询
2025-05-19 08:35:34.243 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 08:35:34.306 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 缙云鸿福康复医院，将使用并发处理单条SQL
2025-05-19 08:36:08.277 | INFO     | __main__:get_total_count:86 - 查询总记录数: 200000
2025-05-19 08:36:08.280 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '缙云鸿福康复医院' 的结果数量较大 (200000 条)，将分 20 页并发查询
2025-05-19 08:43:13.580 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院' 的第 4 到 7 页处理完成
2025-05-19 08:43:34.009 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院' 的第 8 到 11 页处理完成
2025-05-19 08:43:35.316 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院' 的第 0 到 3 页处理完成
2025-05-19 08:43:58.619 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院' 的第 16 到 19 页处理完成
2025-05-19 08:44:05.986 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院' 的第 12 到 15 页处理完成
2025-05-19 08:44:05.996 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 511.76秒
2025-05-19 09:17:55.976 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 09:17:56.689 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 缙云鸿福康复医院住院明细，将使用并发处理单条SQL
2025-05-19 09:18:20.707 | INFO     | __main__:get_total_count:86 - 查询总记录数: 200000
2025-05-19 09:18:20.710 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '缙云鸿福康复医院住院明细' 的结果数量较大 (200000 条)，将分 20 页并发查询
2025-05-19 09:28:30.041 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 4 到 7 页处理完成
2025-05-19 09:29:40.653 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 0 到 3 页处理完成
2025-05-19 09:37:13.192 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 09:37:13.248 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 缙云鸿福康复医院住院明细，将使用并发处理单条SQL
2025-05-19 09:37:45.944 | INFO     | __main__:get_total_count:86 - 查询总记录数: 200000
2025-05-19 09:37:45.948 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '缙云鸿福康复医院住院明细' 的结果数量较大 (200000 条)，将分 20 页并发查询
2025-05-19 09:45:34.033 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 12 到 15 页处理完成
2025-05-19 09:45:58.750 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 0 到 3 页处理完成
2025-05-19 09:46:11.698 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 8 到 11 页处理完成
2025-05-19 09:46:48.741 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 4 到 7 页处理完成
2025-05-19 09:46:58.250 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院住院明细' 的第 16 到 19 页处理完成
2025-05-19 09:46:58.257 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 585.07秒
2025-05-19 10:35:35.390 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 10:35:35.471 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 缙云鸿福康复医院(24.02-24.10)，将使用并发处理单条SQL
2025-05-19 10:36:07.845 | INFO     | __main__:get_total_count:86 - 查询总记录数: 298211
2025-05-19 10:36:07.847 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的结果数量较大 (298211 条)，将分 30 页并发查询
2025-05-19 10:49:56.816 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 10 到 14 页处理完成
2025-05-19 10:50:38.933 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 0 到 4 页处理完成
2025-05-19 10:51:37.789 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 5 到 9 页处理完成
2025-05-19 10:53:19.154 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 15 到 19 页处理完成
2025-05-19 10:53:19.546 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 20 到 24 页处理完成
2025-05-19 10:53:19.598 | INFO     | __main__:process_pages:181 - 规则 '缙云鸿福康复医院(24.02-24.10)' 的第 25 到 29 页处理完成
2025-05-19 10:53:19.611 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 1064.24秒
2025-05-19 15:23:11.848 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 15:23:11.959 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: nan，将使用并发处理单条SQL
2025-05-19 15:23:44.636 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 15:23:44.723 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: “泪道冲洗”重复收取“泪小点扩张”，将使用并发处理单条SQL
2025-05-19 15:29:28.891 | INFO     | __main__:get_total_count:86 - 查询总记录数: 168512
2025-05-19 15:29:28.895 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的结果数量较大 (168512 条)，将分 17 页并发查询
2025-05-19 15:47:38.606 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 15 到 16 页处理完成
2025-05-19 15:47:57.959 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 3 到 5 页处理完成
2025-05-19 15:51:59.313 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 9 到 11 页处理完成
2025-05-19 15:52:36.317 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 12 到 14 页处理完成
2025-05-19 15:52:38.682 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 0 到 2 页处理完成
2025-05-19 15:54:39.749 | INFO     | __main__:process_pages:181 - 规则 '“泪道冲洗”重复收取“泪小点扩张”' 的第 6 到 8 页处理完成
2025-05-19 15:54:39.763 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 1855.14秒
2025-05-19 16:20:45.851 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 16:20:45.935 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: “泪道冲洗”重复“泪小点扩张”，将使用并发处理单条SQL
2025-05-19 16:21:55.760 | INFO     | __main__:get_total_count:86 - 查询总记录数: 176634
2025-05-19 16:21:55.765 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '“泪道冲洗”重复“泪小点扩张”' 的结果数量较大 (176634 条)，将分 18 页并发查询
2025-05-19 16:45:37.134 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 16:45:37.193 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: “泪道冲洗”重复“泪小点扩张”，将使用并发处理单条SQL
2025-05-19 16:45:57.343 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 16:45:57.415 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 为60岁以上患者普遍开展性激素检查，将使用并发处理单条SQL
2025-05-19 16:46:36.946 | INFO     | __main__:get_total_count:86 - 查询总记录数: 10712
2025-05-19 16:46:36.949 | INFO     | __main__:process_single_sql_concurrently:150 - 规则 '为60岁以上患者普遍开展性激素检查' 的结果数量较大 (10712 条)，将分 2 页并发查询
2025-05-19 16:48:34.767 | INFO     | __main__:process_pages:181 - 规则 '为60岁以上患者普遍开展性激素检查' 的第 0 到 0 页处理完成
2025-05-19 16:49:21.667 | INFO     | __main__:process_pages:181 - 规则 '为60岁以上患者普遍开展性激素检查' 的第 1 到 1 页处理完成
2025-05-19 16:49:21.668 | INFO     | __main__:main:373 - 所有规则处理完成,耗时: 204.33秒
2025-05-19 17:09:40.293 | INFO     | __main__:main:370 - 开始处理 1 条规则
2025-05-19 17:09:40.365 | INFO     | __main__:process_rules_concurrently:318 - 只有一条规则: 角膜曲率+A 超检查同一天收费大于1次，将使用并发处理单条SQL
2025-05-19 17:09:40.501 | ERROR    | __main__:get_total_count:89 - 获取总记录数时出错: ParseError: RequestId: 682AF553F215CE7400432881 Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT COUNT(*) AS total_count FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
listagg(distinct t.unit_price,',') within group(order by t.bill_id,t.cost_time) 单价,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
and t.item_code in ('31030003900')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd'),
t.in_time,
t.out_time
having sum(t.num)>1)
ODPS-0130161:[24,1] Parse exception - invalid token 'and'
ODPS-0130161:[24,6] Parse exception - invalid token '.'
2025-05-19 17:09:40.503 | ERROR    | __main__:main:377 - 处理过程中发生错误: ParseError: RequestId: 682AF553F215CE7400432881 Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT COUNT(*) AS total_count FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
listagg(distinct t.unit_price,',') within group(order by t.bill_id,t.cost_time) 单价,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
and t.item_code in ('31030003900')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd'),
t.in_time,
t.out_time
having sum(t.num)>1)
ODPS-0130161:[24,1] Parse exception - invalid token 'and'
ODPS-0130161:[24,6] Parse exception - invalid token '.'
2025-05-19 17:33:50.712 | INFO     | __main__:main:371 - 开始处理 1 条规则
2025-05-19 17:33:50.788 | INFO     | __main__:process_rules_concurrently:319 - 只有一条规则: 流感病毒抗体测（IgM）同一分钟内收费大于1次，将使用并发处理单条SQL
2025-05-19 17:35:48.137 | INFO     | __main__:get_total_count:87 - 查询总记录数: 33191
2025-05-19 17:35:48.140 | INFO     | __main__:process_single_sql_concurrently:151 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的结果数量较大 (33191 条)，将分 4 页并发查询
2025-05-19 17:35:48.233 | ERROR    | __main__:process_pages:181 - 处理规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 0 页时出错: ParseError: RequestId: 682AFB73865B208E00435F14 Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT * FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1) ORDER BY 单据号, LIMIT 0,10000
ODPS-0130161:[45,42] Parse exception - invalid token '0'
2025-05-19 17:35:48.234 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 0 到 0 页处理完成
2025-05-19 17:35:48.340 | ERROR    | __main__:process_pages:181 - 处理规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 1 页时出错: ParseError: RequestId: 682AFB73F215CE74004364AB Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT * FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1) ORDER BY 单据号, LIMIT 10000,10000
ODPS-0130161:[45,42] Parse exception - invalid token '10000'
2025-05-19 17:35:48.343 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 1 到 1 页处理完成
2025-05-19 17:35:48.427 | ERROR    | __main__:process_pages:181 - 处理规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 2 页时出错: ParseError: RequestId: 682AFB73F215CE74004364AC Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT * FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1) ORDER BY 单据号, LIMIT 20000,10000
ODPS-0130161:[45,42] Parse exception - invalid token '20000'
2025-05-19 17:35:48.428 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 2 到 2 页处理完成
2025-05-19 17:35:48.500 | ERROR    | __main__:process_pages:181 - 处理规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 3 页时出错: ParseError: RequestId: 682AFB737DDEBF810043549F Tag: ODPS Endpoint: https://service.cn-hangzhou-zjybhxq-d01.odps.res.zj.hsip.gov.cn/api
SQL Statement: SELECT * FROM (select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1) ORDER BY 单据号, LIMIT 30000,3191
ODPS-0130161:[45,42] Parse exception - invalid token '30000'
2025-05-19 17:35:48.503 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 3 到 3 页处理完成
2025-05-19 17:35:48.504 | INFO     | __main__:main:374 - 所有规则处理完成,耗时: 117.81秒
2025-05-19 17:35:49.183 | ERROR    | __main__:main:378 - 处理过程中发生错误: Invalid Input Error: Python Object "a" of type "str" found on line "c:\Users\<USER>\Desktop\py\dwj.py:33" not suitable for replacement scans.
Make sure that "a" is either a pandas.DataFrame, duckdb.DuckDBPyRelation, pyarrow Table, Dataset, RecordBatchReader, Scanner, or NumPy ndarrays with supported format
2025-05-19 18:53:20.574 | INFO     | __main__:main:371 - 开始处理 1 条规则
2025-05-19 18:53:20.659 | INFO     | __main__:process_rules_concurrently:319 - 只有一条规则: 流感病毒抗体测（IgM）同一分钟内收费大于1次，将使用并发处理单条SQL
2025-05-19 18:54:24.028 | INFO     | __main__:get_total_count:87 - 查询总记录数: 33191
2025-05-19 18:54:24.031 | INFO     | __main__:process_single_sql_concurrently:151 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的结果数量较大 (33191 条)，将分 4 页并发查询
2025-05-19 18:56:45.971 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 3 到 3 页处理完成
2025-05-19 18:57:29.932 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 1 到 1 页处理完成
2025-05-19 18:57:42.079 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 2 到 2 页处理完成
2025-05-19 18:58:13.109 | INFO     | __main__:process_pages:182 - 规则 '流感病毒抗体测（IgM）同一分钟内收费大于1次' 的第 0 到 0 页处理完成
2025-05-19 18:58:13.113 | INFO     | __main__:main:374 - 所有规则处理完成,耗时: 292.55秒
