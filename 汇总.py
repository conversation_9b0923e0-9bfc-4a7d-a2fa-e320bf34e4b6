import os
import duckdb
import os
con = duckdb.connect()
con.install_extension('spatial')#加载execl控制
con.load_extension('spatial')
def walk_all_subdirs(path):
    con.sql("""CREATE TABLE bl (
                                区县 VARCHAR,
                                卫生院机构名称 VARCHAR,
                                住院总费用 VARCHAR,
                                门诊总费用 VARCHAR,
                                违规事项数量 VARCHAR,
                                a类合计 VARCHAR,
                                b类合计 VARCHAR,
                                c类合计 VARCHAR)""")
    for root, dirs, files in os.walk(path):
        for filename in files:
            if  filename.endswith('.xlsx'):
                xlsx_path = os.path.join(root, filename)
                folder_name = os.path.split(root)[1]
                sql = f"""CREATE TABLE hz AS 
        SELECT * FROM st_read('{xlsx_path}',layer = '汇总')"""
                print(sql)
                print(xlsx_path)
                con.sql(sql)
                df = dict(con.sql("""SELECT "住院/门诊",sum(金额) as 金额 from hz group by "住院/门诊" """).fetchall())
                df1 = dict(con.sql("""SELECT 规则分类,sum(金额) as 金额 from hz group by 规则分类 """).fetchall())
                count = con.sql(""" SELECT regexp_extract(规则名称,'\d+') as 规则数量  FROM hz where COALESCE  (序号,'') = '合计'""").fetchone()[0]
                print(count)
                #print(con.sql("""select * from hz """))
                con.sql(f"""insert into bl values ('{folder_name}','{filename[:-8]}',{df.get('住院',0)},{df.get('门诊',0)},{count},{df1.get('A',0)},{df1.get('B',0)},{df1.get('C',0)})""")
                con.sql("""drop table hz""")
    con.sql(f""" COPY (select   * from bl ) to '{os.path.join(path,'汇总数据.xlsx')}' WITH (FORMAT GDAL, DRIVER 'xlsx')""")
    con.sql("""drop table bl""")
#path = input("输入文件夹地址：")
walk_all_subdirs(r"C:\Users\<USER>\Desktop\新建文件夹")