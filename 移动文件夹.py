#读取目录下所有子文件夹
import os
source_folder = r'F:\数据\旧'
new_folder = r'F:\数据\新'
source_subfolders = [f.path for f in os.scandir(source_folder) if f.is_dir()]
#判断子文件夹名称,删除（后面的文字之前是否存在同一个文件夹
for subfolder in source_subfolders:
    subfolder_name = os.path.basename(subfolder)
    if subfolder_name.find('（') > 0:
        print('A')
        new_folder_name = subfolder_name[:subfolder_name.find('（')]
        #判断是否存在同名文件夹
        if os.path.exists(os.path.join(source_folder, new_folder_name)):    
            #如果存在同名文件夹,则移动文件夹并改名
            os.rename(subfolder, os.path.join(new_folder, new_folder_name))
        else:
            pass

