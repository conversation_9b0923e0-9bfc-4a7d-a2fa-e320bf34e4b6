import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from multiprocessing import Pool

def dict_sj():
    try:
        workbook = openpyxl.load_workbook(filename='规则名称.xlsx')
        sheet = workbook['Sheet1']
        data = {row[0]: row[1:] for row in sheet.iter_rows(min_row=2, values_only=True)}
        return data
    except Exception as e:
        print("读取规则名称.xlsx 文件出错:", e)
        return {}

def format_excel(ws):
    header_font = Font(name='Microsoft YaHei UI', size=10, bold=True, color='FFFFFF')
    content_font = Font(name='Microsoft YaHei UI', size=10)
    header_fill = PatternFill(start_color='1f4e78', fill_type='solid')
    content_fill = PatternFill(start_color='D9D9D9', fill_type='solid')

    # 设置表头格式
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # 设置内容格式
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.font = content_font
            cell.fill = content_fill
            cell.alignment = Alignment(horizontal='center', vertical='center')

    # 设置行高和列宽
    ws.row_dimensions[1].height = 35
    for r in range(2, ws.max_row + 1):
        ws.row_dimensions[r].height = 25
    
    dict_width = {'A': 8, 'B': 10, 'C': 12, 'D': 60, 'E': 60, 'F': 10, 'G': 10}
    for col in ws.columns:
        column = col[0].column_letter
        ws.column_dimensions[column].width = dict_width.get(column, 9)
        for i, cell in enumerate(col):
            if i > 0:
                if column == 'A':
                    cell.font = Font(name='Microsoft YaHei UI', size=10, color='0000ff', underline='single')
                elif column in ['D', 'E']:
                    cell.alignment = Alignment(horizontal='left', vertical='center')
                elif column in ['F', 'G']:
                    cell.fill = PatternFill(start_color='fce4d6', fill_type='solid')

    # 设置边框
    thin_border = Border(left=Side(style='thick', color='FFFFFF'),
                         right=Side(style='thick', color='FFFFFF'),
                         top=Side(style='thick', color='FFFFFF'),
                         bottom=Side(style='thick', color='FFFFFF'))
    for row in ws.rows:
        for cell in row:
            cell.border = thin_border

    # 合并D E列最后一行
    ws.merge_cells(start_row=ws.max_row, start_column=4, end_row=ws.max_row, end_column=5)
    for cell in ws[ws.max_row]:
        cell.font = Font(bold=True, name='Microsoft YaHei UI', size=10)
        cell.fill = PatternFill(start_color='f8cbad', fill_type='solid')
    ws.cell(row=ws.max_row, column=4).alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[ws.max_row].height = 40
    ws.freeze_panes = 'A2'

def merge_xlsx_files(source_folder, target_file, data):
    if not os.path.exists(source_folder):
        print("源文件夹不存在！")
        return
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = '汇总'
    ws.append(['序号', '住院/门诊', '规则分类', '规则名称', '规则依据', '数量', '金额'])

    sheet_num = 1
    for filename in os.listdir(source_folder):
        if filename.lower().endswith(".xlsx"):
            try:
                workbook = openpyxl.load_workbook(os.path.join(source_folder, filename), read_only=True)
                for sheetname in workbook.sheetnames:
                    sheet = workbook[sheetname]
                    new_ws = wb.create_sheet(title=f"{sheet_num}")
                    for row in sheet.iter_rows(values_only=True):
                        new_ws.append(row)
                    last_col = new_ws.max_column
                    second_last_col = last_col - 1
                    total_violations = total_amount = 0
                    for row in new_ws.iter_rows(min_row=2,min_col=second_last_col, max_col=last_col):
                        try:
                            total_violations += float(row[0].value)
                            total_amount += float(row[1].value)
                        except ValueError:
                            print(f'错误：{filename}中的最后2列非数字')
                        # if row[second_last_col].value is not None:
                        #     try:
                        #         total_violations += float(row[second_last_col].value)
                        #     except ValueError:
                        #         print(f'错误：{filename}中的{second_last_col}列非数字')
                        # if row[last_col - 1].value is not None:
                        #     try:
                        #         total_amount += float(row[last_col -1].value)
                        #     except ValueError:
                        #         print(f'错误：{filename}中的{last_col - 1}列非数字')
                    gzmc = filename[:-5]
                    ws.append([f'=HYPERLINK("#{sheet_num}!A1", "{sheet_num}")',
                                data.get(gzmc, ['', '', ''])[2],
                                data.get(gzmc, ['', '', ''])[1],
                                gzmc,
                                data.get(gzmc, ['', '', '', '请检查规则名称'])[4],
                                total_violations,
                                total_amount])
                    sheet_num += 1
            except Exception as e:
                print(f'处理文件时出错：{filename}, 错误信息：{e}')

    # 计算总数
    total_violations = sum([float(cell.value) for cell in ws['F'][1:] if cell.value is not None])
    total_amount = sum([float(cell.value) for cell in ws['G'][1:] if cell.value is not None])
    ws.append(['合计', '', '', f'共计-{ws.max_row - 1}-条违规项目', '', total_violations, total_amount])
    format_excel(ws)
    wb.save(target_file)

def traverse_folders(folder_path, save_path, data):
    pool = Pool(processes=int(input("几个进程：")))
    for root, dirs, _ in os.walk(folder_path):
        for name in dirs:
            source_path = os.path.join(root, name)
            result_path = os.path.join(save_path, f'{name}_汇总.xlsx')
            pool.apply_async(merge_xlsx_files, args=(source_path, result_path, data))
    pool.close()
    pool.join()

if __name__ == '__main__':
    source_path = input("请输入要遍历的文件夹地址：")
    save_path = input("请输入要保存的文件夹地址：")
    data = dict_sj()
    traverse_folders(source_path, save_path, data)
    input("输入任意键退出：")
