import duckdb
import sys
import tkinter as tk
from tkinter import filedialog
import pandas as pd

def select_files():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    file_paths = filedialog.askopenfilenames(
        title="选择Excel文件",
        filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
    )
    return file_paths

def process_files(file_paths):
    try:
        conn = duckdb.connect()
        conn.install_extension('excel')
        conn.load_extension('excel')
        
        all_results = []
        
        for file_path in file_paths:
            query = f"""
            SELECT 
                '{file_path.split('/')[-1][:-5]}' AS 文件路径,
                SUM(cast (违规金额 as dec)) AS 金额,
                COUNT(DISTINCT "单据号") AS 人次 
            FROM read_xlsx('{file_path}', header=true, all_varchar = true)
            """
            result = conn.execute(query).fetchdf()
            all_results.append(result)
        
        # 合并所有结果
        final_result = pd.concat(all_results)
        final_result.to_excel(r"C:\Users\<USER>\Desktop\新建文件夹 (2)\统计结果.xlsx", index=False)
        # 输出结果
        print("\n统计结果:")
        print(final_result)
        
        
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        sys.exit(1)

def main():
    print("请选择要统计的Excel文件...")
    file_paths = select_files()
    
    if not file_paths:
        print("未选择文件，程序退出")
        return
    
    process_files(file_paths)

if __name__ == "__main__":
    main()
