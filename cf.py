import duckdb
import os
path = input("输入文件夹地址：")
con = duckdb.connect()
con.install_extension('spatial')#加载execl控制
con.load_extension('spatial')
con.sql(r"""CREATE TABLE datatable AS 
SELECT * FROM st_read('.\数据组-数据字典20220127.xlsx', layer='Sheet1')""")
for path, dir_lst, file_lst in os.walk(path):
    for dir_name in file_lst:
        # print(path + '\\' +  dir_name)
        #print(os.path.join(path, dir_name))
        sql = f"""CREATE TABLE a AS 
SELECT * FROM st_read('{os.path.join(path, dir_name)}',open_options = ['HEADERS=FORCE', 'FIELD_TYPES=STRING'])"""
        con.sql(sql)
        #print(sql)
        df = con.execute('select * from a where 1 = 1').description
        for i in df:
            if i[0] == '险种类型':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'INSUTYPE'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
                con.sql(f"""DELETE FROM a where {i[0]} = '离休人员医疗保障'""")
            if i[0] == '人员类型' or i[0] == '人员类别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'PSN_TYPE'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '患者性别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'GEND'
                                                 ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '费用类别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_CHRGITM_TYPE'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '支付类别' or i[0] == '支付类别b' or i[0] == '支付类别2':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'CHRGITM_LV'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '支付类别' or i[0] == '支付类别b' or i[0] == '支付类别2':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'CHRGITM_LV'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
            if i[0] == '医疗类别':
                con.execute(f"""UPDATE a SET {i[0]} = (SELECT datatable.字典值名称 FROM datatable WHERE a.{i[0]} = datatable.字典值编码 and datatable.字典标识符 =  'MED_TYPE'
                                            ) where exists (select 1 from datatable where a.{i[0]} = datatable.字典值编码)""")
        #print(os.path.join(path, dir_name))
        ifname = con.sql("""SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'a' AND column_name = '定点机构名称' """).fetchall()
        # print(ifname[0][0])
        if ifname[0][0] == 0:
            print(f'{os.path.join(path, dir_name)}没有医疗机构名称')
        else:
            con.sql(f"""delete from a where 定点机构名称 is null """)
            results = con.sql(f"""select * from (
SELECT *,ROW_NUMBER() OVER (PARTITION BY 定点机构编码 order by 定点机构名称 desc) as rn FROM (
select distinct 定点机构名称,定点机构编码 from a)) where rn = 1 """).fetchall()
            for row in results:
                #print(row[0])
                output_dir = os.path.join(path, row[1].strip() + '-'+ row[0].strip())
                output_file = os.path.join(output_dir, dir_name.strip())
                #name = con.sql(f"""select 定点机构名称 from a where 定点机构名称 = '{row[0]}'""").fetchall()
                #output_file = os.path.join(path, name[0][0].strip() + dir_name.strip())
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except Exception as e:
                    print(e)
                    print(output_dir)
                    continue
                sql_name = f""" COPY (select   * from a where trim(定点机构编码) = '{row[1]}') to '{output_file}' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
                #print(sql_name)
                con.sql(sql_name)
        con.sql("""drop table a""")