import os
import pandas as pd
import duckdb
file = r'D:\x'
path = input("输入文件夹地址：")
con = duckdb.connect()
con.install_extension('spatial')  # 加载execl控制
con.load_extension('spatial')
# 递归遍历目录及其子目录
for root, dirs, files in os.walk(file):
    for f in files:
        if f.endswith('.xlsx'):
            #sql_name = f""" COPY (SELECT * FROM st_read('{os.path.join(root, f)}' ,open_options = ['HEADERS=FORCE']) where LEFT(结算日期,4) = '2023' )to '{os.path.join(root, f)}' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
            sql_name = f""" SELECT * FROM st_read('{os.path.join(root, f)}' ,open_options = ['HEADERS=FORCE'])  """
            #print(sql_name)
            try:
                a = con.sql(sql_name)
                #print(f"{a}")
            except:
                #删除
                os.remove(os.path.join(root, f))
                print(f"{os.path.join(root, f)} 删除成功")
  



#

# # 读取并合并所有xls文件
# df_list = [pd.read_excel(file,dtype=str) for file in file_list]
# df = pd.concat(df_list, ignore_index=True)


# # 保存到Excel文件1
# df.to_csv(r'C:\Users\<USER>\Desktop\lq\X\c.csv', index=False)
