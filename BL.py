import duckdb
import os

path = input("输入文件夹地址：")
con = duckdb.connect()
con.install_extension('spatial')  # 加载execl控制
con.load_extension('spatial')
for path, dir_lst, file_lst in os.walk(path):
    for dir_name in file_lst:
        if dir_name.lower().endswith(".xlsx"):
            print(path + '\\' + dir_name)
            sql = f"""CREATE TABLE hz AS 
    SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '汇总',open_options = ['HEADERS=FORCE'])"""
            #print(sql)
            con.sql(sql)
            # con.sql(f"""insert into hz SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '汇总B') """)
            # con.sql(f"""insert into hz SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '汇总C') """)
            con.sql("""DELETE FROM HZ WHERE 规则分类 is null """)
            data = con.sql("""select * from hz where 规则分类 is not null""").fetchall()
            con.sql("""CREATE TABLE bl (
                                        序号 VARCHAR,
                                        类别 VARCHAR,
                                        规则名称 VARCHAR,
                                        结算单据号 VARCHAR,
                                        患者社会保障号码 VARCHAR,
                                        个人编码 VARCHAR,
                                        患者姓名 VARCHAR,
                                        结算日期 VARCHAR,
                                        入院日期 VARCHAR,
                                        出院日期 VARCHAR)""")
            for row in data:
                # 加载违规表
                print(row[0])
                con.sql(f"""CREATE TABLE a AS 
    SELECT * FROM st_read('{os.path.join(path, dir_name)}',layer = '{row[0]}',open_options = ['HEADERS=FORCE'])""")

                # 去重没有结算单据号 异常处理
                try:
                    con.sql(f"""DELETE  from a where ROWID not in (select MAX(ROWID) from a GROUP BY 结算单据号)""")
                except:
                    pass
                # if float(row[6]) > 100000:#取条数
                #     ts = 10
                # else:
                #     ts = 5
                # if row[7] == '1':
                #     ts = 5
                #     print('直查')
                ts = 5

                df = con.execute('select * from a where 1 <> 1').df()
                #print(df)
                if '入院日期' not in df.columns:
                    djh = 'a.结算单据号' if '结算单据号' in df.columns else """''"""
                    sfz = 'a.患者社会保障号码' if '患者社会保障号码' in df.columns else """''"""
                    grbm = 'a.个人编码' if '个人编码' in df.columns else """''"""
                    yllb = 'a.医疗类别' if '医疗类别' in df.columns else """''"""
                    sql_gz = f"""insert into bl select hz.序号,{yllb},HZ.规则名称,{djh},{sfz},{grbm},a.患者姓名,a.结算日期,'','' from a TABLESAMPLE RESERVOIR({ts}), hz where 序号 = {row[0]}"""
                else:
                    djh = 'a.结算单据号' if '结算单据号' in df.columns else """''"""
                    sfz = 'a.患者社会保障号码' if '患者社会保障号码' in df.columns else """''"""
                    grbm = 'a.个人编码' if '个人编码' in df.columns else """''"""
                    yllb = 'a.医疗类别' if '医疗类别' in df.columns else """''"""
                    sql_gz = f"""insert into bl select hz.序号,{yllb},HZ.规则名称,{djh},{sfz},{grbm},a.患者姓名,a.结算日期,a.入院日期,a.出院日期 from a TABLESAMPLE RESERVOIR({ts}), hz where 序号 = {row[0]}"""
                # print(sql_gz)
                #print({row[0]})
                con.sql(sql_gz)
                con.sql("""drop table a""")
            # print(f""" COPY (select   * from bl ) to '{os.path.join(path, dir_name[:-8] + '病历.xlsx')}' WITH (FORMAT GDAL, DRIVER 'xlsx')""")
            # print(con.sql("""select   * from bl """))
            con.sql(
                f""" COPY (select   * from bl ) to '{os.path.join(path, dir_name[:-8] + '病历.xlsx')}' WITH (FORMAT GDAL, DRIVER 'xlsx')""")
            con.sql("""drop table hz""")
            con.sql("""drop table bl""")
