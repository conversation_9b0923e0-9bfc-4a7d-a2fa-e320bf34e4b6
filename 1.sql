


select
              MEDICAL_CODE as 定点机构编码, --定点机构编码
                 MEDICAL_NAME as 定点机构名称, --定点机构名称
                 item_code as 医保目录编码, --医保目录编码
                                ITEM_NAME as 医保目录名称, --医保目录名称
                 ITEM_CODE_HOSP as 机构收费项目编码, --机构收费项目编码
                 ITEM_NAME_HOSP as 机构收费项目名称, --机构收费项目名称
                CHARGE_TYPE as 收费项目类别, --收费项目类别
                COST_TYPE as 费用类别, --费用类别
                sum(NUM) as 数量,
                SUM(MONEY) AS 金额,
                SUM(MONEY_MEDICAL) AS 医保范围费用
                from  hz_yb_fymxxx_20250422 WHERE MEDICAL_CODE = 'P33112400189'
                GROUP BY
                  MEDICAL_CODE , --定点机构编码
                 MEDICAL_NAME , --定点机构名称
                 item_code , --医保目录编码
                                ITEM_NAME , --医保目录名称
                 ITEM_CODE_HOSP , --机构收费项目编码
                 ITEM_NAME_HOSP , --机构收费项目名称
                CHARGE_TYPE,
                  COST_TYPE


select * from hz_yb_fymxxx_20250422



desc quansheng_tjjczq_prd.dwd_fin_setl_d_ls
select * from hz_yb_fymxxx_20250422 where medical_mode <> '%门诊%' AND BILL_ID IN (
SELECT SETL_ID  FROM quansheng_tjjczq_prd.dwd_fin_setl_d_ls WHERE fix_blng_admdvs NOT LIKE '3311%' AND insu_admdvs = '331122'  )

SELECT COUNT(*) FROM  hz_yb_fymxxx_20250422 WHERE BILL_ID IN (
select  DISTINCT BILL_ID from
hz_yb_fymxxx_20250422 where area_code  =  '331122' and MEDICAL_CODE NOT LIKE   '%3311%' and medical_mode not like  '%门诊%' and item_code like '33%')




SELECT 定点机构名称,定点机构编码,SUM(医疗费用总额) AS 医疗费用总额,SUM(基金支付总额) AS 基金支付总额 FROM hz_yb_fyjsxx_20250422
WHERE YEAR(结算时间) >= 2024 AND 定点机构编码 IN ('H33110200425',
'H33110200418',
'H33110200410',
'H33110200394',
'H33110200320',
'H33110200299',
'H33110200381',
'H33110200298',
'H33110200314',
'H33110200357',
'H33110200302',
'H33110200427',
'H33110200321',
'H33110200297',
'H33110200323',
'H33110200304',
'H33110200319',
'H33110200406',
'H33110200322',
'H33110200303',
'H33110200370',
'H33110200315',
'H33110200318',
'H33110200317',
'H33110200794',
'H33110200909',
'H33119900914',
'H33110200883',
'H33110200300',
'H33110200782',
'H33110200316',
'H33110200727',
'H33110200865',
'H33110200869',
'H33110200867',
'H33110200863',
'H33110200866',
'H33110200864',
'H33110200868',
'H33110200861',
'H33110200862',
'H33110200347',
'P33110200116',
'P33110200114',
'P33110200113',
'P33110200057',
'P33110200068',
'P33110200073',
'P33110200064',
'P33110200041',
'P33110200027',
'P33110200269',
'P33110200037',
'P33110200330',
'P33110200280',
'P33110200456',
'P33110200454',
'P33110200107',
'P33110200437',
'P33110200436',
'P33110200528',
'P33110200531')
GROUP BY  定点机构名称,定点机构编码



select DISTINCT
MEDICAL_CODE,MEDICAL_NAME from hz_yb_fymxxx_20250422

select * from hz_zj_024 t where t.bill_id not in
(select bill_id from hz_yb_fymxxx_20250422 t where (t.item_code like '33%' and t.item_code not like '3301%' and (t.item_name not like '%腹腔镜%' and t.item_name not like '经%镜%术'
        and t.item_name not like '%镜%加收')))




from ls_tjjczq_prd.hz_yb_fymxxx_20250422;
select t.medical_name 定点机构名称,YEAR(t.clear_time) 年份,item_code 医保目录编码,ITEM_NAME 医保目录名称,t.medical_mode 医疗类别,t.charge_type 收费项目类别,sum(num) 数量,sum(t.money) 金额 from hz_yb_fymxxx_20250422 t
where medical_code in ('H33112300064')
group by t.medical_name,YEAR(t.clear_time),t.medical_mode,t.charge_type,item_code,ITEM_NAME


select  MEDICAL_NAME AS 医疗机构名称,
        YEAR(clear_time) AS 年度,
        item_code as 医保目录编码,
        ITEM_NAME AS 医保目录名称,
        SUM(NUM) AS 数量,
 sum(MONEY) AS 金额
FROM hz_yb_fymxxx_20250422 where MEDICAL_NAME = '丽水市中心医院' AND ITEM_NAME IN ('神经阻滞麻醉','肛周痔切除或套扎术','心电向量图')
  group by MEDICAL_NAME ,
YEAR(clear_time),
        item_code ,
        ITEM_NAME
select * from hz_yb_fymxxx_20250422 where MEDICAL_NAME = '丽水市中心医院' AND ITEM_NAME IN ('心电向量图')

SELECT * FROM hz_yb_fymxxx_20250422 where medical_code = 'H33119900889' AND ITEM_NAME IN ('脂溶性维生素注射液','中/长链脂肪乳注射液','复方氨基酸注射液') AND YEAR(clear_time) >=2024





SELECT distinct medical_code,medical_name FROM hz_yb_fymxxx_20250422 WHERE medical_code IN ('H33112100174',
'H33112100176',
'H33112100730',
'H33112100180',
'H33112100105',
'H33112100093',
'H33112100187',
'H33112100249') and (OUT_DIAGNOSE_NAME LIKE '%健康查体%' OR IN_DIAGNOSE_NAME LIKE '健康查体') AND MONEY_MEDICAL <> 0



select * from hz_yb_fyjsxx_20250422 where  清算类别 LIKE  '%门诊%'  AND 定点机构编码 IN ('H33112100174',
'H33112100176',
'H33112100730',
'H33112100180',
'H33112100105',
'H33112100093',
'H33112100187',
'H33112100249')
select distinct 医疗机构名称,医疗机构编码,主要诊断名称 from jszd2 where 医疗机构编码 in ('H33112100174',
'H33112100176',
'H33112100730',
'H33112100180',
'H33112100105',
'H33112100093',
'H33112100187',
'H33112100249') and 主要诊断名称 like '%健康%'



select area_code,
       area_person_code,
       medical_code,
       medical_name,
       social_card,
       card_id,
       patient_name,
       hosp_lv,
       benefit_type,
       medical_mode,
       bill_id,
       to_char(cost_time,'yyyy-mm-dd') cost_time,
       clear_time,
       item_code,
       item_name,
       charge_type,
       cost_type,
       unit_price,
       max_price,
       dose,
       out_diagnose_code,
       out_diagnose_name,
              SUM(num) num,
       SUM(money) money,
       SUM(money_medical) money_medical

from ls_tjjczq_prd.hz_yb_fymxxx_20250422 where  medical_code = 'H33119900889'  and item_name IN  ('钾测定','钠测定','氯测定','钙测定')
GROUP BY
area_code,
       area_person_code,
       medical_code,
       medical_name,
       social_card,
       card_id,
       patient_name,
       hosp_lv,
       benefit_type,
       medical_mode,
       bill_id,
       to_char(cost_time,'yyyy-mm-dd') ,
       clear_time,
       item_code,
       item_name,
       charge_type,
       cost_type,
       unit_price,
       max_price,
       dose,
       out_diagnose_code,
       out_diagnose_name
HAVING SUM(num) > 1


SELECT * FROM hz_yb_fymxxx_20250422 WHERE  medical_code = 'H33112300064' AND ITEM_CODE LIKE 'T%'


SELECT DISTINCT      medical_code,
       medical_name FROM hz_yb_fymxxx_20250422 WHERE




SELECT * FROM jsmx2 where 医疗机构名称 IN
('遂昌县湖山中心卫生院','遂昌县湖山乡卫生院（遂昌县人民医院医共体湖山分院）') AND
( 医保项目名称 like '%电针%' )
 AND  YEAR(结算日期) = 2023


select medical_name,MIN(clear_time),MAX(clear_time) from hz_yb_fymxxx_20250422 where medical_name  IN ('遂昌县湖山中心卫生院','遂昌县湖山乡卫生院（遂昌县人民医院医共体湖山分院）')
GROUP BY  medical_name

select 医疗机构名称,医疗机构编码,患者姓名,患者社会保障号码,year(结算日期) as 年度,count(1) AS 次数 from jszd2 where 医疗机构名称 like '景宁大众康寿药店' AND YEAR(结算日期) in ('2024','2023')
GROUP BY 医疗机构名称,医疗机构编码,患者姓名,患者社会保障号码,year(结算日期) order by  count(1) desc



select * from hz_yb_fymxxx_20250422 where bill_id = '330000170081096518201881198405'
ZE

SELECT * FROM JSZD2 WHERE 结算单据号 = '330000170081096518201881198405'

select * from quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls where setl_id = '330000170081096518201881198405'


select * from quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls where MDTRT_ID  = '330000170081096182202416534571'

select * from hz_yb_fyjsxx_20250422 where 结算ID  = '330000170081096518201881198405'

desc quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls




desc jszd2


 select a.*,b.医疗费用总额,b.个人账户支出,B.基金支付总额,基金医疗保险统筹基金支出 FROM
 hz_yb_fyjsxx_20250422 b
 join   hz_yb_fymxxx_20250422 a
 on b.结算id = a.BILL_ID  and b.医疗费用总额 > 500 AND a.medical_code like 'P331125%'



select * from hz_yb_fymxxx t where T.MEDICAL_CODE = 'H33112300064' t.item_code = '42000001300' and t.pay_per_retio<>1 and (t.dept_name||t.discharge_dept_name not like '%中医%'
and t.dept_name||t.discharge_dept_name not like '%骨伤%')

drop table jsmx

select DICT_FIND()


select * from jsmx2 where 医疗机构名称 like ''

select * from hz_yb_fymxxx where medical_code = 'P33110200041' AND YEAR(CLEAR_TIME) >=2024
select * from hz_yb_fyjsxx where 定点机构编码 = 'P33110200041' AND YEAR(结算时间) >=2024

select t.*,
       (case
         when t.item_name in ('彩超常规检查(一个部位)','彩超常规检查（一个部位）') then
          t.money
       end) money_rules
  from hz_yb_fymxxx t
 where exists (select 1
          from hz_yb_fymxxx a
         where a.bill_id = '330000166251888922200558150707' and
            (a.item_name in ('彩超常规检查(一个部位)','彩超常规检查（一个部位）') and a.unit_price in (60,70))
           and t.bill_id = a.bill_id
     and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd' )
           and t.medical_code = a.medical_code    )
   and exists (select 1
          from hz_yb_fymxxx a where bill_id = '330000166251888922200558150707' and
          (a.item_name LIKE '脏器灰阶立体成像'  and a.unit_price = 100) -- 编码3307/3308/3310
     and a.item_code not in  ('彩超常规检查(一个部位)','彩超常规检查（一个部位）')
           and t.bill_id = a.bill_id
           and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code)
   and ((t.item_name in ('彩超常规检查(一个部位)','彩超常规检查（一个部位）')  and t.unit_price in (60,70)) or t.item_name LIKE '脏器灰阶立体成像'  and t.unit_price = 100)



 select * from jsmx2 where 医保项目编码 = 'XL01XEA298A001020201359' AND year(结算日期) = 2022
select DISTINCT  医疗机构编码,医疗机构名称 from jszd2 where 医疗机构编码 like 'P3311%'

SELECT distinct 医疗机构名称,医疗机构编码,医疗机构统筹编码 FROM JSZD2 WHERE 医疗机构编码 IN ('H33112700543','H33112700235','H33112300749','H33112700241','H33112700648')

select * from hz_yb_fymxxx t where t.medical_code = 'H33112400307' AND t.item_code = '42000001300' and t.pay_per_retio<>1

select * from hz_yb_fymxxx where medical_name in ('遂昌县焦滩乡蔡口村卫生室','遂昌县云峰街道连头村卫生室','遂昌县北界镇淤弓村卫生室','遂昌县垵口乡垵口村卫生所','遂昌县金竹镇王村村卫生室') and year(clear_time) >=2023


select 医疗机构编码,医疗机构名称,医院项目编码,医院项目名称,医保项目编码,医保项目名称,SUM(数量) as 数量,SUM(金额) as 金额 from jsmx2
where 医保项目名称   like '沉香曲%'  AND YEAR(结算日期) in ('2023','2024') AND 医疗机构统筹编码 = '331122' GROUP BY 医疗机构编码,医疗机构名称,医院项目编码,医院项目名称,医保项目编码,医保项目名称

desc quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls

select * from quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls where init_feedetl_sn is not null
select medical_name,MEDICAL_CODE,COUNT(*) from hz_yb_fymxxx t where t.MEDICAL_NAME='%鸿福康复%'

CREATE INDEX idx_hz_yb_fymxxx ON hz_yb_fymxxx (pay_per_retio, item_name, item_code, bill_id, cost_time, medical_code);
select medical_name,MEDICAL_CODE,COUNT(*) from hz_yb_fymxxx t where t.MEDICAL_NAME like  '%鸿福康复%'
group by medical_name,MEDICAL_CODE
select * from hz_yb_fymxxx where MEDICAL_CODE = 'H33112200070' and to_char(clear_time,'YYYY-MM') BETWEEN '2024-02' and '2024-10'
