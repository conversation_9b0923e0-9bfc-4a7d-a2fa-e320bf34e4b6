#import duckdb as duckdb
import duckdb
import os
con = duckdb.connect()
con.install_extension('spatial')#加载execl控制
con.load_extension('spatial')
con.sql(r"""CREATE TABLE datatable AS 
SELECT * FROM st_read('.\中英对照.xlsx', open_options = ['HEADERS=FORCE', 'FIELD_TYPES=STRING'])""")

def cf(path):
    # 获取目录中所有CSV文件的列表
    csv_files = [f for f in os.listdir(path) if f.endswith('.csv')]
    # 按前缀将CSV文件进行分组
    csv_dict = {}
    for csv_file in csv_files:
        if '_page_' in csv_file:
            prefix = csv_file.split('_page_')[0]
        else:
            prefix = os.path.splitext(csv_file)[0]
        if prefix not in csv_dict:
            csv_dict[prefix] = [csv_file]
        else:
            csv_dict[prefix].append(csv_file)
    # print(csv_dict)

    # print(sql)

    grouped_dir = os.path.join(path, '拆分')
    os.makedirs(grouped_dir, exist_ok=True)
    for prefix in csv_dict:
        csv_files = csv_dict[prefix]
        csv_files_with_path = [path + f for f in csv_files]
        # print(csv_files)
        a = f"""
    CREATE table a AS 
    select
    	*
    from
    	read_csv_auto({csv_files_with_path},
    	all_varchar = TRUE)
       """
        print(a)
        try:
            con.sql(a)
        except Exception as e:
            print(a)
            print(e)
            continue
        df = con.execute('SELECT d.*,a.column_name FROM datatable d left join (DESCRIBE select * FROM a) a on d.A = a.column_name where a.column_name is not null').df()
        print(df)
        # 循环遍历df的每一行. 修改表a的列名为 datatable.b  
        for index, row in df.iterrows():
            xxx = f"""ALTER TABLE a RENAME COLUMN {row['A']} TO {row['B']}"""
            print(xxx)
            con.sql(xxx)   
            ifname = con.sql("""SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'a' AND column_name = '定点机构名称' """).fetchall()
            # print(ifname[0][0])
        if ifname[0][0] == 0:
            print(f'{prefix}')
        else:
            results = con.sql(f"""select DISTINCT trim(定点机构名称) from a""").fetchall()
            for row in results:
                output_dir = os.path.join(grouped_dir, row[0].strip())
                output_file = os.path.join(output_dir, prefix.strip())
                #文件名加上医院名称
                #output_file = os.path.join(output_dir, row[0].strip() + prefix.strip())
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except Exception as e:
                    print(e)
                    print(output_dir)
                    continue
                sql_name = f""" COPY (select   * from a where trim(定点机构名称) = '{row[0]}') to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
                # 三目,加上匹配度
                # sql_name = f""" COPY (select   *,ROUND(jaro_similarity(医院项目名称,医保项目名称) * 100 ,2) || '%' AS 匹配度 from a where trim(医疗机构名称) = '{row[0]}') to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""

                # print(sql_name)
                con.sql(sql_name)
        con.sql("""drop table a""")



if __name__ == '__main__':
    path = input("输入文件夹地址：")
    #判断地址最后面是否带\
    if path[-1] != "\\":
        path = path + "\\"
    cf(path)
    input("输入任意键退出：")
