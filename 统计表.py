from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
import pandas as pd
import os

def modify_header(file_path, header_mapping):
    # 使用只读模式打开文件,减少内存占用
    wb = load_workbook(filename=file_path, read_only=True)
    # 创建新的工作簿用于写入
    new_wb = load_workbook(filename=file_path)
    
    for sheet_name in wb.sheetnames:
        if sheet_name == "汇总":
            continue
            
        ws = wb[sheet_name]
        new_ws = new_wb[sheet_name]
        
        # 只处理表头行
        header_row = next(ws.rows)
        for col, cell in enumerate(header_row, 1):
            if cell.value in header_mapping:
                new_ws.cell(row=1, column=col).value = header_mapping[cell.value]
    
    # 保存并关闭
    new_wb.save(file_path)
    new_wb.close()
    wb.close()

# 只读取需要的A和B列
df = pd.read_excel("中英对照.xlsx", usecols=["A", "B"])
header_mapping = df.set_index("A").to_dict()["B"]

folder_path = r"C:\Users\<USER>\Desktop\新建文件夹\拆分"
# 逐个处理文件
for file_name in os.listdir(folder_path):
    if file_name.endswith(".xlsx"):
        print('处理文件: ' + file_name)
        try:
            full_path = os.path.join(folder_path, file_name)
            modify_header(full_path, header_mapping)
        except Exception as e:
            print(f'处理文件 {file_name} 时出错: {str(e)}')