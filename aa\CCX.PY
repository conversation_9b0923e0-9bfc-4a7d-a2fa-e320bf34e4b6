from odps import ODPS
from odps.tunnel import TableTunnel
from odps import options

options.verify_ssl = False


def download_odps_table_to_csv(odps_access_id, odps_secret_access_key, project_name, endpoint, table_name, output_file, sep=',', partition_spec=None):
   
    # 初始化ODPS入口
    o = ODPS(odps_access_id, odps_secret_access_key, project_name, endpoint=endpoint)

    # 获取表的实例
    table = o.get_table(table_name)
    for table in o.list_tables():
        print(table.name)
    # t = o.get_table(table_name)
    # with o.execute_sql('select * from zyzd limit 0,10000').open_reader(tunnel=True) as reader:
    #     pd_df = reader.to_pandas()
    #     print(pd_df)
    #     pd_df.to_csv(output_file, sep=sep, index=False)

# 示例调用
download_odps_table_to_csv(
    odps_access_id='LTAI4FyVWDuA73Cw6eHZYMf7',
    odps_secret_access_key='******************************',
    project_name='DF_ch_146061',
    endpoint='http://service.cn-hangzhou.maxcompute.aliyun-inc.com/api',
    table_name='zyzd',
    output_file='output.csv',
    sep=',',
    #endpoint='http://service.cn-hangzhou-zjybhxq-d01.odps.ops.zj.hsip.gov.cn/api'
    #partition_spec={'partition_column': 'partition_value'}  # 对于分区表，需要指定分区信息
)