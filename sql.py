from sqlalchemy import create_engine
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
from loguru import logger
import threading

# 配置Oracle连接池
ORACLE_CONN_STR = "oracle+cx_oracle://LSHQYY:LSHQYY@127.0.0.1:1521/ORCL"
engine = create_engine(
    ORACLE_CONN_STR,
    pool_size=5,
    max_overflow=10,
    pool_recycle=3600
)

class Counter:
    def __init__(self):
        self._success = 0
        self._empty = 0
        self._error = 0
        self._lock = threading.Lock()
    
    def inc_success(self):
        with self._lock:
            self._success += 1
    
    def inc_empty(self):
        with self._lock:
            self._empty += 1
    
    def inc_error(self):
        with self._lock:
            self._error += 1
    
    def get_counts(self):
        with self._lock:
            return (self._success, self._empty, self._error)

def execute_sql_task(sql, rule_name, counter, errors):
    try:
        logger.info(f"正在执行规则: {rule_name}")
        
        with engine.connect() as conn:
            df = pd.read_sql_query(sql, conn)
        
        if not df.empty:
            output_path = f"G:/bc/{rule_name}.xlsx"
            df.to_excel(output_path, index=False)
            logger.success(f"规则 {rule_name} 执行完成，保存到 {output_path}")
            counter.inc_success()
            return True
        else:
            logger.info(f"规则 {rule_name} 无查询结果")
            counter.inc_empty()
            return False
    except Exception as e:
        error_msg = str(e)
        logger.error(f"执行规则 {rule_name} 时发生错误: {error_msg}")
        with errors.lock:
            errors.data.append({
                "规则名称": rule_name,
                "SQL语句": sql,
                "错误信息": error_msg
            })
        counter.inc_error()
        return False

def main(excel_path, max_workers=5):
    # 创建共享数据结构
    class Errors:
        def __init__(self):
            self.data = []
            self.lock = threading.Lock()
    
    counter = Counter()
    errors = Errors()
    
    df_tasks = pd.read_excel(excel_path)
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for _, row in df_tasks.iterrows():
            future = executor.submit(
                execute_sql_task,
                row['sql'],
                row['rule_name'],
                counter,
                errors
            )
            futures.append(future)
        
        # 等待所有任务完成
        for future in futures:
            future.result()
    
    # 保存错误日志
    if errors.data:
        error_df = pd.DataFrame(errors.data)
        error_path = "G:/bc/errors.xlsx"
        error_df.to_excel(error_path, index=False)
        logger.error(f"共发现 {len(error_df)} 个错误，已保存到 {error_path}")
    
    # 输出统计信息
    success, empty, error = counter.get_counts()
    total = success + empty + error
    logger.info(f"""
    执行完成汇总:
    总任务数: {total}
    ✔ 成功保存: {success}
    ○ 空结果: {empty}
    × 错误数: {error}
    """)

if __name__ == "__main__":
    logger.add("sql_executor.log", rotation="10 MB")
    excel_file = r"C:\Users\<USER>\Desktop\fly_rule_jm1.xlsx"
    main(excel_file, max_workers=5)
